/**
 * @definition    ProcessFileByType    {"category":"ext"}
 * @description   按类型处理文件  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    entype    文件类型编码
 * @param    {JSON}    record    记录数据
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    if (!record) {
        throw "记录数据不能为空";
    }
    
    var fileInfo = {
        pdfFileName: "",
        pdfFilePath: "",
        pdfFileFormat: "",
        excelFileName: "",
        excelFilePath: "",
        excelFileFormat: "",
        fileName: "",
        filePath: "",
        fileFormat: ""
    };
    
    // 根据文件类型处理文件
    if (entype === "CheckCard") {
        // 处理检查卡的PDF和Excel文件
        if (record.pdfDownloadUrl && record.pdfDownloadUrl !== "") {
            var pdfResult = me.DownloadFile({
                url: record.pdfDownloadUrl,
                expectedFormat: "pdf"
            });
            
            if (pdfResult.success) {
                fileInfo.pdfFileName = pdfResult.data.fileName;
                fileInfo.pdfFilePath = pdfResult.data.filePath;
                fileInfo.pdfFileFormat = pdfResult.data.fileFormat || "pdf";
            } else {
                logger.warn("ProcessFileByType-PDF下载失败：" + pdfResult.msg);
            }
        }
        
        if (record.excelDownloadUrl && record.excelDownloadUrl !== "") {
            var excelResult = me.DownloadFile({
                url: record.excelDownloadUrl,
                expectedFormat: "xlsx"
            });
            
            if (excelResult.success) {
                fileInfo.excelFileName = excelResult.data.fileName;
                fileInfo.excelFilePath = excelResult.data.filePath;
                fileInfo.excelFileFormat = excelResult.data.fileFormat || "xlsx";
            } else {
                logger.warn("ProcessFileByType-Excel下载失败：" + excelResult.msg);
            }
        }
        
    } else if (entype === "Photo") {
        // 处理照片文件
        if (record.downloadUrl && record.downloadUrl !== "") {
            var photoResult = me.ProcessPhotoFile({
                url: record.downloadUrl
            });
            
            if (photoResult.success) {
                fileInfo.filePath = photoResult.data.filePath;
                fileInfo.fileFormat = photoResult.data.fileFormat;
            } else {
                logger.warn("ProcessFileByType-照片处理失败：" + photoResult.msg);
            }
        }
        
    } else if (entype === "TechCard" || entype === "TechProblem" || 
               entype === "ScrapNotice" || entype === "ProductSubmit" || 
               entype === "UndeliveredProduct" || entype === "UniversalTrackingCard") {
        // 处理其他类型文件
        if (record.downloadUrl && record.downloadUrl !== "") {
            var fileResult = me.DownloadFile({
                url: record.downloadUrl,
                expectedFormat: null // 不限制格式
            });
            
            if (fileResult.success) {
                fileInfo.fileName = fileResult.data.fileName;
                fileInfo.filePath = fileResult.data.filePath;
                fileInfo.fileFormat = fileResult.data.fileFormat;
            } else {
                logger.warn("ProcessFileByType-文件下载失败：" + fileResult.msg);
            }
        }
    }
    
    res.success = true;
    res.data = fileInfo;
    res.msg = "文件处理完成";
    
} catch (error) {
    res.success = false;
    var msg = "ProcessFileByType-文件处理失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
