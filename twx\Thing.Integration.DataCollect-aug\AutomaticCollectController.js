/**
 * @definition    AutomaticCollectController    {"category":"ext"}
 * @description   自动采集主控制器（重构版）  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID    {"aspect.defaultValue":"0"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };
var startTime = new Date().getTime();
var processedCount = 0;

try {
    // 1. 获取MES接口配置
    var configResult = me.GetSystemConfig({
        configName: 'MES项目清单接口路径',
        parentName: '系统配置'
    });

    if (!configResult.success) {
        throw "获取MES接口配置失败：" + configResult.msg;
    }

    var reqUrl = configResult.data.configValue;

    // 2. 更新旧数据状态
    var updateStatusResult = me.UpdateStatusBatch({
        treeid: treeid
    });

    if (!updateStatusResult.success) {
        logger.warn("AutomaticCollectController-更新旧数据状态失败：" + updateStatusResult.msg);
    }

    // 3. 查询待同步数据
    var syncDataResult = me.QuerySyncData({
        treeid: treeid
    });

    if (!syncDataResult.success) {
        throw "查询待同步数据失败：" + syncDataResult.msg;
    }

    var syncDataList = syncDataResult.data;
    if (syncDataList.length === 0) {
        throw "未找到待同步数据";
    }

    // 4. 按节点分组处理数据
    var nodeGroups = {};
    for (var i = 0; i < syncDataList.length; i++) {
        var item = syncDataList[i];
        var key = item.refTreeId + "_" + item.tableType;//过程节点id+表类型<设计、工艺、过程、质量>

        if (!nodeGroups[key]) {
            nodeGroups[key] = {
                refTreeId: item.refTreeId,
                tableType: item.tableType,
                items: []
            };
        }
        nodeGroups[key].items.push(item);
    }

    // 5. 处理每个节点组
    for (var groupKey in nodeGroups) {
        var group = nodeGroups[groupKey];

        try {
            var groupResult = me.ProcessNodeGroup({
                reqUrl: reqUrl,
                nodeGroup: group,
                treeid: treeid
            });

            if (groupResult.success) {
                processedCount += groupResult.data.processedCount;
            } else {
                logger.error("AutomaticCollectController-处理节点组失败：" + groupResult.msg + "，节点=" + group.refTreeId);
            }

        } catch (groupError) {
            logger.error("AutomaticCollectController-处理节点组异常：" + groupError + "，节点=" + group.refTreeId);
        }
    }

    // 6. 计算耗时
    var endTime = new Date().getTime();
    var duration = endTime - startTime;

    var timeResult = me.FormatDuration({
        milliseconds: duration
    });

    var durationStr = timeResult.success ? timeResult.data.formattedTime : (duration + "毫秒");

    res.success = true;
    res.data = {
        processedCount: processedCount,
        totalCount: syncDataList.length,
        duration: duration,
        durationStr: durationStr
    };
    res.msg = "自动采集完成，成功处理" + processedCount + "条数据，耗时：" + durationStr;

    // 记录操作信息
    logger.info("AutomaticCollectController-自动采集完成，treeid=" + treeid + "，处理=" + processedCount + "，总计=" + syncDataList.length + "，耗时=" + durationStr);

} catch (error) {
    var endTime = new Date().getTime();
    var duration = endTime - startTime;

    res.success = false;
    var msg = "AutomaticCollectController-自动采集失败，原因：" + error;
    logger.error(msg + "，treeid=" + treeid + "，已处理=" + processedCount);
    res.msg = "成功同步" + processedCount + "条数据后，中断操作：" + error;

    res.data = {
        processedCount: processedCount,
        totalCount: 0,
        duration: duration,
        error: String(error)
    };
}

result = res;
