/**
 * @definition    ConfigService    {"category":"ext"}
 * @description   配置服务，提供系统配置相关功能  wanghq 2025年8月21日19:38:00
 * @implementation    {Script}
 *
 * @param    {STRING}    configName    配置项名称
 * @param    {STRING}    configType    配置项类型
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: ""
};

try {
    var configName = configName || "";
    var configType = configType || "系统配置";

    if (configName === "") {
        throw "配置项名称不能为空";
    }

    logger.info("ConfigService-获取配置：" + configName + "，类型：" + configType);

    // 获取配置项值
    var configValue = Things["Thing.Fn.SystemDic"].getKeyByNames({
        name: configName,
        pname: configType
    });

    if (configValue === null || configValue === undefined || configValue === "") {
        throw "配置项不存在或为空：" + configName;
    }

    result.data = configValue;
    result.success = true;
    result.msg = "配置获取成功";

    logger.info("ConfigService-配置获取成功：" + configName + " = " + configValue);

} catch (error) {
    result.success = false;
    var msg = "ConfigService-配置获取失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;