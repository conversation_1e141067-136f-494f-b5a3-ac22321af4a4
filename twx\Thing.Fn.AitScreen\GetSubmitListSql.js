/**
 * @definition    GetSubmitListSql
 * @description   获取查询产品交接单的sql wanghq 2023年4月2日00:11:15
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 *
 * @returns    {STRING}
 */
var fileType = "产品交接单";
var whereSql = me.GetWhereSql({
    treeId: treeId,
    startDate: startDate,
    endDate: endDate,
    fileType: fileType
});
var nullUnitText = '未设置';
var nullText = "未知";
var sql = "select * " +
    "from (select row_number() over (partition by PRODUCTNAME,PRODUCTCODE,BATCHCODE,SUBMITUNIT order by SOURCE_ID desc) groupno, " +
    "             x.* " +
    "      from (select m.ID, " +
    "                   n.PRODUCTNAME, " +
    "                   n.PRODUCTCODE, " +
    "                   n.BATCHCODE, " +
    "                   n.SOURCE_ID, " +
    "                   m.NODECODE, " +
    "                   m.TABLENAME, " +
    "                   m.FILE_FORMAT, " +
    "                   m.FILE_NAME, " +
    "                   m.FILE_TYPE, " +
    "                   m.FILEPATH, " +
    "                   m.CREATE_TIMESTAMP, " +
    "                   nvl(n.ISCERTIFICATE, '" + nullText + "') AS ISCERTIFICATE, " +
    "                   nvl(n.ISLUOHAN, '" + nullText + "')      AS ISLUOHAN, " +
    "                   n.CERTIFICATENUMBER, " +
    "                   n.RESUMENUMBER, " +
    "                   n.OTHER_CERTIFICATE1, " +
    "                   n.OTHER_CERTIFICATE2, " +
    "                   n.ISSUBMIT, " +
    "                   n.LUOHANPHASE, " +
    "                   nvl(n.SUBMITUNIT, '" + nullUnitText + "')   AS SUBMITUNIT " +
    "            from (select ID, " +
    "                         NODECODE, " +
    "                         TABLENAME, " +
    "                         FILEPATH, " +
    "                         FILE_FORMAT, " +
    "                         FILE_NAME, " +
    "                         FILE_TYPE, " +
    "                         CREATE_TIMESTAMP " +
    "                  from RESULTGATHER " + whereSql + ") m " +
    "                     left join V_LATEST_PRODUCT_SUBMIT n " +
    "                               on n.SOURCE_ID like '%' || m.TABLENAME || '%' and n.RESULT_ID = m.ID where n.SUBMITUNIT not like '%812%') x) " +
    "where groupno = 1 order by CREATE_TIMESTAMP desc";
result = sql;