/**
 * @definition    AutomaticCollect    {"category":"ext"}
 * @description   自动采集服务 - 重构版本，负责从MES系统自动采集数据并保存到数据库  wanghq 2025年8月21日19:40:00
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID，0表示处理所有节点
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: {
        totalRecords: 0,
        successCount: 0,
        errorCount: 0,
        processingTime: 0,
        details: []
    }
};

var startTime = new Date().getTime();

try {
    var treeid = treeid || 0;

    logger.info("AutomaticCollect-开始自动采集，treeid：" + treeid);

    // 1. 获取接口路径
    var configResult = me.ConfigService({
        configName: 'MES项目清单接口路径',
        configType: '系统配置'
    });

    if (!configResult.success || !configResult.data) {
        throw "无法获取MES接口路径：" + configResult.msg;
    }

    var reqUrl = configResult.data;

    // 2. 更新结果状态为old（重置之前的数据状态）
    me.UtilService({
        operation: "updateResultStatus",
        params: { treeid: treeid }
    });

    // 3. 查询待采集数据
    var queryResult = me.DataQueryService({
        treeid: treeid
    });

    if (!queryResult.success) {
        throw queryResult.msg;
    }

    var allWaitToSyncTable = queryResult.data;
    result.data.totalRecords = allWaitToSyncTable.length;

    if (allWaitToSyncTable.length === 0) {
        result.success = true;
        result.msg = "无待采集数据";
        result = result;
        return;
    }

    logger.info("AutomaticCollect-找到 " + allWaitToSyncTable.length + " 条待采集数据");

    // 4. 处理每条数据
    var successCount = 0;
    var errorCount = 0;
    var details = [];

    for (var i = 0; i < allWaitToSyncTable.length; i++) {
        try {
            var processResult = me.RecordProcessService({
                row: allWaitToSyncTable[i],
                reqUrl: reqUrl,
                treeid: treeid
            });

            if (processResult.success) {
                successCount++;
                details.push({
                    nodecode: allWaitToSyncTable[i].NODECODE,
                    status: "success",
                    message: "处理成功",
                    recordsCount: processResult.data.recordsCount
                });
            } else {
                errorCount++;
                details.push({
                    nodecode: allWaitToSyncTable[i].NODECODE,
                    status: "error",
                    message: processResult.msg
                });
            }
        } catch (recordError) {
            errorCount++;
            details.push({
                nodecode: allWaitToSyncTable[i].NODECODE,
                status: "error",
                message: "处理异常：" + recordError
            });
            logger.error("AutomaticCollect-处理记录异常：" + recordError);
        }
    }

    // 5. 计算处理时间
    var endTime = new Date().getTime();
    var processingTime = endTime - startTime;

    var timeResult = me.UtilService({
        operation: "formatTime",
        params: { msec: processingTime }
    });

    var timeStr = timeResult.success ? timeResult.data : (processingTime + "ms");

    // 6. 设置返回结果
    result.data.successCount = successCount;
    result.data.errorCount = errorCount;
    result.data.processingTime = processingTime;
    result.data.details = details;

    result.success = true;
    result.msg = "自动采集完成，成功：" + successCount + "，失败：" + errorCount + "，耗时：" + timeStr;

    logger.info("AutomaticCollect-自动采集完成，成功：" + successCount + "，失败：" + errorCount + "，耗时：" + timeStr);

} catch (error) {
    var endTime = new Date().getTime();
    var processingTime = endTime - startTime;

    var timeResult = me.UtilService({
        operation: "formatTime",
        params: { msec: processingTime }
    });

    var timeStr = timeResult.success ? timeResult.data : (processingTime + "ms");

    result.success = false;
    result.msg = "自动采集失败，耗时：" + timeStr + "，原因：" + error;
    result.data.processingTime = processingTime;

    logger.error("AutomaticCollect-自动采集失败：" + error);
}

result = result;