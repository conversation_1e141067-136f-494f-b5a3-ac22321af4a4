/**
 * @definition    GetFileTypeMapping    {"category":"ext"}
 * @description   获取文件类型映射关系  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    fileType    文件类型名称
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!fileType || fileType === "") {
        throw "文件类型不能为空";
    }
    
    // 获取文件类型映射
    var entype = Things["Thing.Fn.SystemDic"].getFileTypeByName({
        name: fileType,
        type: '自动采集清单'
    });
    
    // 验证映射是否存在
    if (entype === null || entype === undefined || entype === "") {
        res.success = false;
        res.msg = "未找到文件类型映射：" + fileType;
        res.data = {
            fileType: fileType,
            entype: "",
            isValid: false
        };
    } else {
        res.success = true;
        res.msg = "获取文件类型映射成功";
        res.data = {
            fileType: fileType,
            entype: entype,
            isValid: true
        };
    }
    
} catch (error) {
    res.success = false;
    var msg = "GetFileTypeMapping-获取文件类型映射失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
