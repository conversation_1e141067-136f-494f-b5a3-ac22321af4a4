/**
 * @definition    ParseMesResponse    {"category":"ext"}
 * @description   解析MES响应数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    responseXml    MES响应XML
 * @param    {STRING}    entype    文件类型编码
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: [] };

try {
    // 参数验证
    if (!responseXml || responseXml === "") {
        throw "响应XML不能为空";
    }
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    
    // 解析XML
    var xml;
    try {
        xml = new XML(responseXml);
    } catch (xmlError) {
        throw "XML解析失败：" + xmlError;
    }
    
    // 获取文件名称列名
    var fileNameColumn = me.GetFileNameColumn({
        entype: entype
    });
    
    if (!fileNameColumn.success) {
        throw "获取文件名称列失败：" + fileNameColumn.msg;
    }
    
    var fileNameCol = fileNameColumn.data.columnName;
    var recordList = [];
    
    // 遍历解析记录
    for each(var tag in xml.Record) {
        var sourceId = String(tag.Id);
        
        // 跳过空的源ID
        if (!sourceId || sourceId === "" || sourceId === "undefined") {
            continue;
        }
        
        var productId = String(tag.ProductId || "");
        var fileName = String(tag[fileNameCol] || "");
        
        // 构建记录对象
        var record = {
            sourceId: sourceId,
            productId: productId,
            fileName: fileName,
            securityLevel: String(tag.SecurityLevel || ""),
            rawData: tag // 保留原始数据用于后续处理
        };
        
        // 根据文件类型添加特定字段
        if (entype === "CheckCard") {
            record.pdfDownloadUrl = String(tag.PDFDownLoadURL || "");
            record.excelDownloadUrl = String(tag.ExcelDownLoadURL || "");
        } else if (entype === "Photo") {
            record.downloadUrl = String(tag.DownLoadURL || "");
        } else if (entype === "TechCard" || entype === "TechProblem" || 
                   entype === "ScrapNotice" || entype === "ProductSubmit" || 
                   entype === "UndeliveredProduct" || entype === "UniversalTrackingCard") {
            record.downloadUrl = String(tag.DownLoadURL || "");
        }
        
        recordList.push(record);
    }
    
    res.success = true;
    res.data = recordList;
    res.msg = "解析MES响应成功，共解析" + recordList.length + "条记录";
    
    // 记录解析信息
    logger.info("ParseMesResponse-解析成功，entype=" + entype + "，记录数=" + recordList.length);
    
} catch (error) {
    res.success = false;
    var msg = "ParseMesResponse-解析MES响应失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
