<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Entities build="b68" majorVersion="8" minorVersion="3" modelPersistenceProviderPackage="PostgresPersistenceProviderPackage" revision="9" schemaVersion="1032" universal=""><Things><Thing description="" documentationContent="" effectiveThingPackage="ConfiguredThing" enabled="true" homeMashup="" identifier="" lastModifiedDate="2025-08-22T08:23:57.257+08:00" name="Thing.Integration.DataCollect-aug" projectName="" published="false" tags="" thingTemplate="GenericThing" valueStream=""><avatar/><DesignTimePermissions><Create/><Read/><Update/><Delete/><Metadata/></DesignTimePermissions><RunTimePermissions/><VisibilityPermissions><Visibility/></VisibilityPermissions><ConfigurationTableDefinitions/><ConfigurationTables/><ThingShape><PropertyDefinitions/><ServiceDefinitions><ServiceDefinition name="AutomaticCollectController" description="自动采集主控制器（重构版）  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeid" description="树节点ID" ordinal="0" aspect.defaultValue="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="BatchInsertResults" description="批量插入结果数据  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="tableType" description="表类型" ordinal="0"/><FieldDefinition baseType="JSON" name="resultData" description="结果数据列表" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="FormatDuration" description="格式化时间间隔  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="milliseconds" description="毫秒数" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="GetCurrentTimeString" description="获取当前时间字符串  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/></ServiceDefinition><ServiceDefinition name="GetFileNameColumn" description="根据文件类型获取文件名称列名  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="entype" description="文件类型编码" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="GetFileTypeMapping" description="获取文件类型映射关系  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="fileType" description="文件类型名称" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="GetSystemConfig" description="获取系统配置信息  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="configName" description="配置名称" ordinal="0"/><FieldDefinition baseType="STRING" name="parentName" description="父级配置名称" ordinal="1" aspect.defaultValue="系统配置"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="GetTableMapping" description="获取表名映射关系  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="category" description="数据类别（设计、工艺、过程控制、质量综合）" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="ParseMesResponse" description="解析MES响应数据  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="responseXml" description="MES响应XML" ordinal="0"/><FieldDefinition baseType="STRING" name="entype" description="文件类型编码" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="PostMesRequest" description="发送MES请求  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="reqUrl" description="请求URL" ordinal="0"/><FieldDefinition baseType="STRING" name="processCode" description="过程代码" ordinal="1"/><FieldDefinition baseType="STRING" name="category" description="数据类别" ordinal="2"/><FieldDefinition baseType="STRING" name="entype" description="文件类型编码" ordinal="3"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="ProcessFileByType" description="按类型处理文件  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="entype" description="文件类型编码" ordinal="0"/><FieldDefinition baseType="JSON" name="record" description="记录数据" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="ProcessNodeGroup" description="处理节点组数据  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="reqUrl" description="MES请求URL" ordinal="0"/><FieldDefinition baseType="JSON" name="nodeGroup" description="节点组数据" ordinal="1"/><FieldDefinition baseType="NUMBER" name="treeid" description="树节点ID" ordinal="2"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="ProcessRecordList" description="批量处理记录列表  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="JSON" name="recordList" description="记录列表" ordinal="0"/><FieldDefinition baseType="STRING" name="entype" description="文件类型编码" ordinal="1"/><FieldDefinition baseType="STRING" name="resultTableName" description="结果表名" ordinal="2"/><FieldDefinition baseType="JSON" name="nodeData" description="节点数据" ordinal="3"/><FieldDefinition baseType="NUMBER" name="treeid" description="树节点ID" ordinal="4"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="QueryExistingData" description="批量查询已存在数据  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="entype" description="文件类型编码" ordinal="0"/><FieldDefinition baseType="JSON" name="sourceIds" description="源ID列表" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="QuerySyncData" description="批量查询待同步数据  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeid" description="树节点ID" ordinal="0" aspect.defaultValue="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="SqlEscape" description="SQL转义处理  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="value" description="需要转义的值" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition name="UpdateStatusBatch" description="批量更新数据状态为old  wanghq 2025年8月21日18:37:14" aspect.isAsync="false" category="ext" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeid" description="树节点ID" ordinal="0" aspect.defaultValue="0"/></ParameterDefinitions></ServiceDefinition></ServiceDefinitions><EventDefinitions/><ServiceMappings/><ServiceImplementations><ServiceImplementation name="AutomaticCollectController" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    AutomaticCollectController    {"category":"ext"}
 * @description   自动采集主控制器（重构版）  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID    {"aspect.defaultValue":"0"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };
var startTime = new Date().getTime();
var processedCount = 0;

try {
    // 1. 获取MES接口配置
    var configResult = me.GetSystemConfig({
        configName: 'MES项目清单接口路径',
        parentName: '系统配置'
    });

    if (!configResult.success) {
        throw "获取MES接口配置失败：" + configResult.msg;
    }

    var reqUrl = configResult.data.configValue;

    // 2. 更新旧数据状态
    var updateStatusResult = me.UpdateStatusBatch({
        treeid: treeid
    });

    if (!updateStatusResult.success) {
        logger.warn("AutomaticCollectController-更新旧数据状态失败：" + updateStatusResult.msg);
    }

    // 3. 查询待同步数据
    var syncDataResult = me.QuerySyncData({
        treeid: treeid
    });

    if (!syncDataResult.success) {
        throw "查询待同步数据失败：" + syncDataResult.msg;
    }

    var syncDataList = syncDataResult.data;
    if (syncDataList.length === 0) {
        throw "未找到待同步数据";
    }

    // 4. 按节点分组处理数据
    var nodeGroups = {};
    for (var i = 0; i &lt; syncDataList.length; i++) {
        var item = syncDataList[i];
        var key = item.refTreeId + "_" + item.tableType;//过程节点id+表类型&lt;设计、工艺、过程、质量&gt;

        if (!nodeGroups[key]) {
            nodeGroups[key] = {
                refTreeId: item.refTreeId,
                tableType: item.tableType,
                items: []
            };
        }
        nodeGroups[key].items.push(item);
    }

    // 5. 处理每个节点组
    for (var groupKey in nodeGroups) {
        var group = nodeGroups[groupKey];

        try {
            var groupResult = me.ProcessNodeGroup({
                reqUrl: reqUrl,
                nodeGroup: group,
                treeid: treeid
            });

            if (groupResult.success) {
                processedCount += groupResult.data.processedCount;
            } else {
                logger.error("AutomaticCollectController-处理节点组失败：" + groupResult.msg + "，节点=" + group.refTreeId);
            }

        } catch (groupError) {
            logger.error("AutomaticCollectController-处理节点组异常：" + groupError + "，节点=" + group.refTreeId);
        }
    }

    // 6. 计算耗时
    var endTime = new Date().getTime();
    var duration = endTime - startTime;

    var timeResult = me.FormatDuration({
        milliseconds: duration
    });

    var durationStr = timeResult.success ? timeResult.data.formattedTime : (duration + "毫秒");

    res.success = true;
    res.data = {
        processedCount: processedCount,
        totalCount: syncDataList.length,
        duration: duration,
        durationStr: durationStr
    };
    res.msg = "自动采集完成，成功处理" + processedCount + "条数据，耗时：" + durationStr;

    // 记录操作信息
    logger.info("AutomaticCollectController-自动采集完成，treeid=" + treeid + "，处理=" + processedCount + "，总计=" + syncDataList.length + "，耗时=" + durationStr);

} catch (error) {
    var endTime = new Date().getTime();
    var duration = endTime - startTime;

    res.success = false;
    var msg = "AutomaticCollectController-自动采集失败，原因：" + error;
    logger.error(msg + "，treeid=" + treeid + "，已处理=" + processedCount);
    res.msg = "成功同步" + processedCount + "条数据后，中断操作：" + error;

    res.data = {
        processedCount: processedCount,
        totalCount: 0,
        duration: duration,
        error: String(error)
    };
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="BatchInsertResults" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    BatchInsertResults    {"category":"ext"}
 * @description   批量插入结果数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    tableType    表类型
 * @param    {JSON}    resultData    结果数据列表
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: [] };

try {
    // 参数验证
    if (!tableType || tableType === "") {
        throw "表类型不能为空";
    }
    if (!resultData || !resultData.rows || resultData.rows.length === 0) {
        res.success = true;
        res.data = [];
        res.msg = "结果数据列表为空";
        result = res;
        return;
    }
    
    var insertedIds = [];
    var seqName = "";
    
    // 根据表类型确定序列名
    if (tableType === "DESIGN_DATA_RESULT") {
        seqName = "design_cm_sequence.nextval";
    } else if (tableType === "CRAFT_DATA_RESULT") {
        seqName = "craft_cm_sequence.nextval";
    } else if (tableType === "PROCESS_CONTROL_RESULT") {
        seqName = "process_cm_sequence.nextval";
    } else if (tableType === "QUALITY_CONTROL_RESULT") {
        seqName = "quality_cm_sequence.nextval";
    } else {
        throw "不支持的表类型：" + tableType;
    }
    
    // 获取当前时间
    var nowTime = me.GetCurrentTimeString();
    if (!nowTime.success) {
        throw "获取当前时间失败：" + nowTime.msg;
    }
    var timeStr = nowTime.data.timeString;
    
    // 批量构建INSERT语句
    var insertSqls = [];
    
    for (var i = 0; i &lt; resultData.rows.length; i++) {
        var row = resultData.rows[i];
        
        // SQL转义处理
        var nodeCode = me.SqlEscape({ value: row.NODECODE || "" }).data.escapedValue;
        var nodeName = me.SqlEscape({ value: row.NODENAME || "" }).data.escapedValue;
        var fileNumber = me.SqlEscape({ value: row.FILE_NUMBER || "" }).data.escapedValue;
        var fileName = me.SqlEscape({ value: row.FILE_NAME || "" }).data.escapedValue;
        var fileType = me.SqlEscape({ value: row.FILE_TYPE || "" }).data.escapedValue;
        var gatheringMethod = me.SqlEscape({ value: row.GATHERING_METHOD || "" }).data.escapedValue;
        var sourceSystem = me.SqlEscape({ value: row.SOURCE_SYSTEM || "" }).data.escapedValue;
        var deliveryState = me.SqlEscape({ value: row.DELIVERY_STATE || "" }).data.escapedValue;
        var securityLevel = me.SqlEscape({ value: row.SECURITY_LEVEL || "" }).data.escapedValue;
        var filePath = me.SqlEscape({ value: row.FILEPATH || "" }).data.escapedValue;
        var filename = me.SqlEscape({ value: row.FILENAME || "" }).data.escapedValue;
        var stateCheck = me.SqlEscape({ value: row.STATE_CHECK || "" }).data.escapedValue;
        var productId = me.SqlEscape({ value: row.PRODUCT_ID || "" }).data.escapedValue;
        var status = me.SqlEscape({ value: row.STATUS || "" }).data.escapedValue;
        var fileFormat = me.SqlEscape({ value: row.FILE_FORMAT || "" }).data.escapedValue;
        var docInfo = me.SqlEscape({ value: row.DOC_INFO || "" }).data.escapedValue;
        
        var insertSql = "insert into " + tableType + 
            " (ID, NODECODE, NODENAME, FILE_NUMBER, FILE_NAME, FILE_TYPE, " +
            "GATHERING_METHOD, SOURCE_SYSTEM, DELIVERY_STATE, SECURITY_LEVEL, " +
            "FILEPATH, FILENAME, STATE_CHECK, PRODUCT_ID, STATUS, FILE_FORMAT, " +
            "DOC_INFO, CREATE_TIMESTAMP, CREATOR) values (" +
            seqName + ", '" + nodeCode + "', '" + nodeName + "', '" + fileNumber + "', '" +
            fileName + "', '" + fileType + "', '" + gatheringMethod + "', '" +
            sourceSystem + "', '" + deliveryState + "', '" + securityLevel + "', '" +
            filePath + "', '" + filename + "', '" + stateCheck + "', '" +
            productId + "', '" + status + "', '" + fileFormat + "', '" +
            docInfo + "', '" + timeStr + "', 'wanghq')";
        
        insertSqls.push(insertSql);
    }
    
    // 批量执行插入
    for (var j = 0; j &lt; insertSqls.length; j++) {
        try {
            Things['Thing.DB.Oracle'].RunCommand({
                sql: insertSqls[j]
            });
            insertedIds.push(j + 1); // 记录成功插入的索引
        } catch (insertError) {
            logger.error("BatchInsertResults-插入失败，索引=" + j + "，错误：" + insertError);
        }
    }
    
    res.success = true;
    res.data = insertedIds;
    res.msg = "批量插入完成，成功插入" + insertedIds.length + "条，总计" + insertSqls.length + "条";
    
    // 记录操作信息
    logger.info("BatchInsertResults-批量插入完成，表=" + tableType + "，成功=" + insertedIds.length + "，总计=" + insertSqls.length);
    
} catch (error) {
    res.success = false;
    var msg = "BatchInsertResults-批量插入失败，原因：" + error;
    logger.error(msg + "，表=" + tableType);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="FormatDuration" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    FormatDuration    {"category":"ext"}
 * @description   格式化时间间隔  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    milliseconds    毫秒数
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (milliseconds === null || milliseconds === undefined || milliseconds &lt; 0) {
        throw "毫秒数必须为非负数";
    }
    
    var msec = Number(milliseconds);
    
    // 计算各个时间单位
    var days = Math.floor(msec / (1000 * 60 * 60 * 24));
    var hours = Math.floor((msec % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    var minutes = Math.floor((msec % (1000 * 60 * 60)) / (1000 * 60));
    var seconds = Math.floor((msec % (1000 * 60)) / 1000);
    var remainingMs = msec % 1000;
    
    var formattedTime = '';
    
    // 根据时间长度选择合适的显示格式
    if (days &gt; 0) {
        formattedTime = days + '天' + hours + '时' + minutes + '分' + seconds + '秒';
    } else if (hours &gt; 0) {
        formattedTime = hours + '时' + minutes + '分' + seconds + '秒';
    } else if (minutes &gt; 0) {
        formattedTime = minutes + '分' + seconds + '秒';
    } else if (seconds &gt; 0) {
        formattedTime = seconds + '秒';
    } else {
        formattedTime = msec + '毫秒';
    }
    
    res.success = true;
    res.data = {
        originalMilliseconds: msec,
        formattedTime: formattedTime,
        components: {
            days: days,
            hours: hours,
            minutes: minutes,
            seconds: seconds,
            milliseconds: remainingMs
        }
    };
    res.msg = "时间格式化成功";
    
} catch (error) {
    res.success = false;
    var msg = "FormatDuration-时间格式化失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="GetCurrentTimeString" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetCurrentTimeString    {"category":"ext"}
 * @description   获取当前时间字符串  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 获取当前时间
    var now = new Date();
    
    // 格式化时间字符串 yyyy-MM-dd HH:mm:ss
    var year = now.getFullYear();
    var month = (now.getMonth() + 1);
    var day = now.getDate();
    var hour = now.getHours();
    var minute = now.getMinutes();
    var second = now.getSeconds();
    
    // 补零处理
    if (month &lt; 10) month = "0" + month;
    if (day &lt; 10) day = "0" + day;
    if (hour &lt; 10) hour = "0" + hour;
    if (minute &lt; 10) minute = "0" + minute;
    if (second &lt; 10) second = "0" + second;
    
    var timeString = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
    
    res.success = true;
    res.data = {
        timeString: timeString,
        timestamp: now.getTime()
    };
    res.msg = "获取当前时间成功";
    
} catch (error) {
    res.success = false;
    var msg = "GetCurrentTimeString-获取当前时间失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="GetFileNameColumn" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetFileNameColumn    {"category":"ext"}
 * @description   根据文件类型获取文件名称列名  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    entype    文件类型编码
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    
    var columnName = "";
    
    // 根据文件类型映射列名
    if (entype === "TechCard") {
        columnName = "AsmTaskName";
    } else if (entype === "CheckCard") {
        columnName = "CheckCardName";
    } else if (entype === "TechProblem") {
        columnName = "BillCode";
    } else if (entype === "Photo") {
        columnName = "PhotoName";
    } else if (entype === "ScrapNotice") {
        columnName = "BillCode";
    } else if (entype === "MaterialDelivery") {
        columnName = "BillCode";
    } else if (entype === "ProductSubmit") {
        columnName = "BillCode";
    } else if (entype === "UndeliveredProduct") {
        columnName = "BillCode";
    } else if (entype === "ProductOutIn") {
        columnName = "BillCode";
    } else if (entype === "UniversalTrackingCard") {
        columnName = "TaskName";
    } else {
        // 默认使用BillCode
        columnName = "BillCode";
        logger.warn("GetFileNameColumn-未知文件类型，使用默认列名：" + entype);
    }
    
    res.success = true;
    res.data = {
        entype: entype,
        columnName: columnName
    };
    res.msg = "获取文件名称列名成功";
    
} catch (error) {
    res.success = false;
    var msg = "GetFileNameColumn-获取文件名称列名失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="GetFileTypeMapping" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetFileTypeMapping    {"category":"ext"}
 * @description   获取文件类型映射关系  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    fileType    文件类型名称
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!fileType || fileType === "") {
        throw "文件类型不能为空";
    }
    
    // 获取文件类型映射
    var entype = Things["Thing.Fn.SystemDic"].getFileTypeByName({
        name: fileType,
        type: '自动采集清单'
    });
    
    // 验证映射是否存在
    if (entype === null || entype === undefined || entype === "") {
        res.success = false;
        res.msg = "未找到文件类型映射：" + fileType;
        res.data = {
            fileType: fileType,
            entype: "",
            isValid: false
        };
    } else {
        res.success = true;
        res.msg = "获取文件类型映射成功";
        res.data = {
            fileType: fileType,
            entype: entype,
            isValid: true
        };
    }
    
} catch (error) {
    res.success = false;
    var msg = "GetFileTypeMapping-获取文件类型映射失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="GetSystemConfig" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetSystemConfig    {"category":"ext"}
 * @description   获取系统配置信息  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    configName    配置名称
 * @param    {STRING}    parentName    父级配置名称    {"aspect.defaultValue":"系统配置"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!configName || configName === "") {
        throw "配置名称不能为空";
    }
    
    // 调用系统字典服务获取配置
    var configValue = Things["Thing.Fn.SystemDic"].getKeyByNames({
        name: configName,
        pname: parentName || "系统配置"
    });
    
    // 验证配置是否存在
    if (configValue === null || configValue === undefined || configValue === "") {
        throw "未找到配置项：" + configName;
    }
    
    res.success = true;
    res.data = {
        configName: configName,
        parentName: parentName || "系统配置",
        configValue: configValue
    };
    res.msg = "获取配置成功";
    
} catch (error) {
    res.success = false;
    var msg = "GetSystemConfig-获取配置失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="GetTableMapping" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetTableMapping    {"category":"ext"}
 * @description   获取表名映射关系  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    category    数据类别（设计、工艺、过程控制、质量综合）
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!category || category === "") {
        throw "数据类别不能为空";
    }
    
    var tableType = "";
    var isValid = true;
    
    // 根据类别映射表名
    if (category === "设计") {
        tableType = "DESIGN_DATA_RESULT";
    } else if (category === "工艺") {
        tableType = "CRAFT_DATA_RESULT";
    } else if (category === "质量综合") {
        tableType = "QUALITY_CONTROL_RESULT";
    } else if (category === "过程控制") {
        tableType = "PROCESS_CONTROL_RESULT";
    } else {
        isValid = false;
        tableType = "";
    }
    
    if (!isValid) {
        res.success = false;
        res.msg = "不支持的数据类别：" + category;
    } else {
        res.success = true;
        res.msg = "获取表名映射成功";
    }
    
    res.data = {
        category: category,
        tableType: tableType,
        isValid: isValid
    };
    
} catch (error) {
    res.success = false;
    var msg = "GetTableMapping-获取表名映射失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="ParseMesResponse" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    ParseMesResponse    {"category":"ext"}
 * @description   解析MES响应数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    responseXml    MES响应XML
 * @param    {STRING}    entype    文件类型编码
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: [] };

try {
    // 参数验证
    if (!responseXml || responseXml === "") {
        throw "响应XML不能为空";
    }
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    
    // 解析XML
    var xml;
    try {
        xml = new XML(responseXml);
    } catch (xmlError) {
        throw "XML解析失败：" + xmlError;
    }
    
    // 获取文件名称列名
    var fileNameColumn = me.GetFileNameColumn({
        entype: entype
    });
    
    if (!fileNameColumn.success) {
        throw "获取文件名称列失败：" + fileNameColumn.msg;
    }
    
    var fileNameCol = fileNameColumn.data.columnName;
    var recordList = [];
    
    // 遍历解析记录
    for each(var tag in xml.Record) {
        var sourceId = String(tag.Id);
        
        // 跳过空的源ID
        if (!sourceId || sourceId === "" || sourceId === "undefined") {
            continue;
        }
        
        var productId = String(tag.ProductId || "");
        var fileName = String(tag[fileNameCol] || "");
        
        // 构建记录对象
        var record = {
            sourceId: sourceId,
            productId: productId,
            fileName: fileName,
            securityLevel: String(tag.SecurityLevel || ""),
            rawData: tag // 保留原始数据用于后续处理
        };
        
        // 根据文件类型添加特定字段
        if (entype === "CheckCard") {
            record.pdfDownloadUrl = String(tag.PDFDownLoadURL || "");
            record.excelDownloadUrl = String(tag.ExcelDownLoadURL || "");
        } else if (entype === "Photo") {
            record.downloadUrl = String(tag.DownLoadURL || "");
        } else if (entype === "TechCard" || entype === "TechProblem" || 
                   entype === "ScrapNotice" || entype === "ProductSubmit" || 
                   entype === "UndeliveredProduct" || entype === "UniversalTrackingCard") {
            record.downloadUrl = String(tag.DownLoadURL || "");
        }
        
        recordList.push(record);
    }
    
    res.success = true;
    res.data = recordList;
    res.msg = "解析MES响应成功，共解析" + recordList.length + "条记录";
    
    // 记录解析信息
    logger.info("ParseMesResponse-解析成功，entype=" + entype + "，记录数=" + recordList.length);
    
} catch (error) {
    res.success = false;
    var msg = "ParseMesResponse-解析MES响应失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="PostMesRequest" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    PostMesRequest    {"category":"ext"}
 * @description   发送MES请求  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    reqUrl    请求URL
 * @param    {STRING}    processCode    过程代码
 * @param    {STRING}    category    数据类别
 * @param    {STRING}    entype    文件类型编码
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!reqUrl || reqUrl === "") {
        throw "请求URL不能为空";
    }
    if (!processCode || processCode === "") {
        throw "过程代码不能为空";
    }
    if (!category || category === "") {
        throw "数据类别不能为空";
    }
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    
    // 构建SOAP请求内容
    var content = '&lt;?xml version="1.0" encoding="utf-8"?&gt;' +
        '&lt;soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ' +
        'xmlns:xsd="http://www.w3.org/2001/XMLSchema" ' +
        'xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"&gt;' +
        '&lt;soap:Body&gt;' +
        '&lt;GetProcessListInfo xmlns="http://tempuri.org/"&gt;' +
        '&lt;xmlSend&gt;&amp;lt;Request&amp;gt;&amp;lt;ProcessCode&amp;gt;' + processCode + 
        '&amp;lt;/ProcessCode&amp;gt;&amp;lt;Category&amp;gt;' + category + 
        '&amp;lt;/Category&amp;gt;&amp;lt;Type&amp;gt;' + entype + 
        '&amp;lt;/Type&amp;gt;&amp;lt;/Request&amp;gt;&lt;/xmlSend&gt;' +
        '&lt;/GetProcessListInfo&gt;' +
        '&lt;/soap:Body&gt;' +
        '&lt;/soap:Envelope&gt;';
    
    // 构建请求参数
    var params = {
        headers: {
            "Content-Type": "text/xml; charset=utf-8"
        },
        url: reqUrl,
        timeout: 60000, // 60秒超时
        content: content
    };
    
    // 发送请求
    var resultXml = Resources["ContentLoaderFunctions"].PostXML(params);
    
    // 验证响应
    if (!resultXml) {
        throw "MES系统返回空响应";
    }
    
    // 解析返回的XML
    var contentXml = resultXml.*::Body.*::GetProcessListInfoResponse;
    if (!contentXml) {
        throw "无法解析MES响应XML结构";
    }
    
    var responseXml = String(contentXml.*::GetProcessListInfoResult);
    if (!responseXml || responseXml === "") {
        throw "MES响应结果为空";
    }
    
    // 提取Response部分
    var startIndex = responseXml.indexOf("&lt;Response");
    var endIndex = responseXml.indexOf("&lt;/Response&gt;") + 11;
    
    if (startIndex === -1 || endIndex === -1) {
        throw "MES响应格式不正确，缺少Response标签";
    }
    
    var finalXml = responseXml.substring(startIndex, endIndex);
    
    res.success = true;
    res.data = {
        processCode: processCode,
        category: category,
        entype: entype,
        responseXml: finalXml
    };
    res.msg = "MES请求发送成功";
    
    // 记录请求信息
    logger.info("PostMesRequest-请求成功，processCode=" + processCode + "，category=" + category + "，entype=" + entype);
    
} catch (error) {
    res.success = false;
    var msg = "PostMesRequest-MES请求失败，原因：" + error;
    logger.error(msg + "，processCode=" + processCode + "，category=" + category + "，entype=" + entype);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="ProcessFileByType" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    ProcessFileByType    {"category":"ext"}
 * @description   按类型处理文件  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    entype    文件类型编码
 * @param    {JSON}    record    记录数据
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    if (!record) {
        throw "记录数据不能为空";
    }
    
    var fileInfo = {
        pdfFileName: "",
        pdfFilePath: "",
        pdfFileFormat: "",
        excelFileName: "",
        excelFilePath: "",
        excelFileFormat: "",
        fileName: "",
        filePath: "",
        fileFormat: ""
    };
    
    // 根据文件类型处理文件
    if (entype === "CheckCard") {
        // 处理检查卡的PDF和Excel文件
        if (record.pdfDownloadUrl &amp;&amp; record.pdfDownloadUrl !== "") {
            var pdfResult = me.DownloadFile({
                url: record.pdfDownloadUrl,
                expectedFormat: "pdf"
            });
            
            if (pdfResult.success) {
                fileInfo.pdfFileName = pdfResult.data.fileName;
                fileInfo.pdfFilePath = pdfResult.data.filePath;
                fileInfo.pdfFileFormat = pdfResult.data.fileFormat || "pdf";
            } else {
                logger.warn("ProcessFileByType-PDF下载失败：" + pdfResult.msg);
            }
        }
        
        if (record.excelDownloadUrl &amp;&amp; record.excelDownloadUrl !== "") {
            var excelResult = me.DownloadFile({
                url: record.excelDownloadUrl,
                expectedFormat: "xlsx"
            });
            
            if (excelResult.success) {
                fileInfo.excelFileName = excelResult.data.fileName;
                fileInfo.excelFilePath = excelResult.data.filePath;
                fileInfo.excelFileFormat = excelResult.data.fileFormat || "xlsx";
            } else {
                logger.warn("ProcessFileByType-Excel下载失败：" + excelResult.msg);
            }
        }
        
    } else if (entype === "Photo") {
        // 处理照片文件
        if (record.downloadUrl &amp;&amp; record.downloadUrl !== "") {
            var photoResult = me.ProcessPhotoFile({
                url: record.downloadUrl
            });
            
            if (photoResult.success) {
                fileInfo.filePath = photoResult.data.filePath;
                fileInfo.fileFormat = photoResult.data.fileFormat;
            } else {
                logger.warn("ProcessFileByType-照片处理失败：" + photoResult.msg);
            }
        }
        
    } else if (entype === "TechCard" || entype === "TechProblem" || 
               entype === "ScrapNotice" || entype === "ProductSubmit" || 
               entype === "UndeliveredProduct" || entype === "UniversalTrackingCard") {
        // 处理其他类型文件
        if (record.downloadUrl &amp;&amp; record.downloadUrl !== "") {
            var fileResult = me.DownloadFile({
                url: record.downloadUrl,
                expectedFormat: null // 不限制格式
            });
            
            if (fileResult.success) {
                fileInfo.fileName = fileResult.data.fileName;
                fileInfo.filePath = fileResult.data.filePath;
                fileInfo.fileFormat = fileResult.data.fileFormat;
            } else {
                logger.warn("ProcessFileByType-文件下载失败：" + fileResult.msg);
            }
        }
    }
    
    res.success = true;
    res.data = fileInfo;
    res.msg = "文件处理完成";
    
} catch (error) {
    res.success = false;
    var msg = "ProcessFileByType-文件处理失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="ProcessNodeGroup" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    ProcessNodeGroup    {"category":"ext"}
 * @description   处理节点组数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    reqUrl    MES请求URL
 * @param    {JSON}    nodeGroup    节点组数据
 * @param    {NUMBER}    treeid    树节点ID
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!reqUrl || reqUrl === "") {
        throw "MES请求URL不能为空";
    }
    if (!nodeGroup || !nodeGroup.items || nodeGroup.items.length === 0) {
        throw "节点组数据不能为空";
    }
    
    var processedCount = 0;
    var refTreeId = nodeGroup.refTreeId;
    var tableType = nodeGroup.tableType;
    
    // 获取表名映射
    var tableMappingResult = me.GetTableMapping({
        category: tableType
    });
    
    if (!tableMappingResult.success) {
        throw "获取表名映射失败：" + tableMappingResult.msg;
    }
    
    //存放同步结果数据的表名
    var resultTableName = tableMappingResult.data.tableType;
    
    // 按文件类型分组处理
    var fileTypeGroups = {};
    for (var i = 0; i &lt; nodeGroup.items.length; i++) {
        var item = nodeGroup.items[i];
        var fileType = item.fileType;
        
        if (!fileTypeGroups[fileType]) {
            fileTypeGroups[fileType] = [];
        }
        fileTypeGroups[fileType].push(item);
    }
    
    // 处理每个文件类型组
    for (var fileType in fileTypeGroups) {
        var items = fileTypeGroups[fileType];
        
        try {
            // 获取文件类型映射
            var fileTypeMappingResult = me.GetFileTypeMapping({
                fileType: fileType
            });
            
            if (!fileTypeMappingResult.success || !fileTypeMappingResult.data.isValid) {
                logger.warn("ProcessNodeGroup-跳过无效文件类型：" + fileType);
                continue;
            }
            
            var entype = fileTypeMappingResult.data.entype;
            
            // 发送MES请求
            var mesRequestResult = me.PostMesRequest({
                reqUrl: reqUrl,
                processCode: String(refTreeId),
                category: tableType,
                entype: entype
            });
            
            if (!mesRequestResult.success) {
                logger.error("ProcessNodeGroup-MES请求失败：" + mesRequestResult.msg + "，文件类型=" + fileType);
                continue;
            }
            
            // 解析MES响应
            var parseResult = me.ParseMesResponse({
                responseXml: mesRequestResult.data.responseXml,
                entype: entype
            });
            
            if (!parseResult.success) {
                logger.error("ProcessNodeGroup-解析MES响应失败：" + parseResult.msg + "，文件类型=" + fileType);
                continue;
            }
            
            var recordList = parseResult.data;
            if (recordList.length === 0) {
                logger.info("ProcessNodeGroup-MES返回空数据，文件类型=" + fileType);
                continue;
            }
            
            // 处理记录数据
            var processResult = me.ProcessRecordList({
                recordList: recordList,
                entype: entype,
                resultTableName: resultTableName,
                nodeData: items[0], // 使用第一个节点的数据作为基础信息
                treeid: treeid
            });
            
            if (processResult.success) {
                processedCount += processResult.data.processedCount;
            } else {
                logger.error("ProcessNodeGroup-处理记录数据失败：" + processResult.msg + "，文件类型=" + fileType);
            }
            
        } catch (fileTypeError) {
            logger.error("ProcessNodeGroup-处理文件类型异常：" + fileTypeError + "，文件类型=" + fileType);
        }
    }
    
    res.success = true;
    res.data = {
        processedCount: processedCount,
        refTreeId: refTreeId,
        tableType: tableType,
        fileTypeCount: Object.keys(fileTypeGroups).length
    };
    res.msg = "节点组处理完成，处理" + processedCount + "条记录";
    
    // 记录处理信息
    logger.info("ProcessNodeGroup-节点组处理完成，refTreeId=" + refTreeId + "，处理=" + processedCount);
    
} catch (error) {
    res.success = false;
    var msg = "ProcessNodeGroup-处理节点组失败，原因：" + error;
    logger.error(msg + "，refTreeId=" + (nodeGroup ? nodeGroup.refTreeId : "unknown"));
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="ProcessRecordList" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    ProcessRecordList    {"category":"ext"}
 * @description   批量处理记录列表  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {INFOTABLE}    recordList    记录列表
 * @param    {STRING}    entype    文件类型编码
 * @param    {STRING}    resultTableName    结果表名
 * @param    {JSON}    nodeData    节点数据
 * @param    {NUMBER}    treeid    树节点ID
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!recordList || recordList.length === 0) {
        res.success = true;
        res.data = { processedCount: 0 };
        res.msg = "记录列表为空";
        result = res;
        return;
    }
    
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    
    if (!resultTableName || resultTableName === "") {
        throw "结果表名不能为空";
    }
    
    if (!nodeData) {
        throw "节点数据不能为空";
    }
    
    // 1. 构建源ID列表用于批量查询已存在数据
    var sourceIdTable = Resources["InfoTableFunctions"].CreateInfoTableFromDataShape({
        infoTableName: "SourceIdList",
        dataShapeName: "StringList"
    });
    
    for (var i = 0; i &lt; recordList.length; i++) {
        var record = recordList[i];
        if (record.sourceId &amp;&amp; record.sourceId !== "") {
            var sourceIdWithTable = record.sourceId + '_' + resultTableName;
            sourceIdTable.AddRow({ name: sourceIdWithTable });
        }
    }
    
    // 2. 批量查询已存在数据
    var existingDataResult = me.QueryExistingData({
        entype: entype,
        sourceIds: sourceIdTable
    });
    
    if (!existingDataResult.success) {
        throw "批量查询已存在数据失败：" + existingDataResult.msg;
    }
    
    var existingDataMap = existingDataResult.data;
    
    // 3. 分类处理记录：新增和更新
    var newRecords = [];
    var updateRecords = [];
    var processedCount = 0;
    
    for (var j = 0; j &lt; recordList.length; j++) {
        var record = recordList[j];
        var sourceIdWithTable = record.sourceId + '_' + resultTableName;
        
        // 跳过无效记录
        if (!record.sourceId || record.sourceId === "") {
            continue;
        }
        
        // 处理文件下载
        var fileProcessResult = me.ProcessFileByType({
            entype: entype,
            record: record
        });
        
        if (!fileProcessResult.success) {
            logger.warn("ProcessRecordList-文件处理失败：" + fileProcessResult.msg + "，sourceId=" + record.sourceId);
            continue;
        }
        
        var fileInfo = fileProcessResult.data;
        
        // 构建记录数据
        var recordData = {
            sourceId: sourceIdWithTable,
            nodeCode: nodeData.nodeCode,
            nodeName: nodeData.nodeCode,
            fileName: record.fileName,
            fileType: nodeData.fileType,
            gatheringMethod: "自动采集",
            sourceSystem: "MES",
            deliveryState: nodeData.deliveryState,
            securityLevel: record.securityLevel,
            stateCheck: "未确认",
            status: "new",
            productId: record.productId,
            fileInfo: fileInfo,
            rawRecord: record
        };
        
        // 判断是新增还是更新
        if (existingDataMap[sourceIdWithTable]) {
            recordData.existingData = existingDataMap[sourceIdWithTable];
            updateRecords.push(recordData);
        } else {
            newRecords.push(recordData);
        }
        
        processedCount++;
    }
    
    // 4. 批量处理新增记录
    if (newRecords.length &gt; 0) {
        var insertResult = me.BatchProcessNewRecords({
            records: newRecords,
            resultTableName: resultTableName,
            entype: entype,
            treeid: treeid
        });
        
        if (!insertResult.success) {
            logger.error("ProcessRecordList-批量处理新增记录失败：" + insertResult.msg);
        }
    }
    
    // 5. 批量处理更新记录
    if (updateRecords.length &gt; 0) {
        var updateResult = me.BatchProcessUpdateRecords({
            records: updateRecords,
            resultTableName: resultTableName,
            entype: entype,
            treeid: treeid
        });
        
        if (!updateResult.success) {
            logger.error("ProcessRecordList-批量处理更新记录失败：" + updateResult.msg);
        }
    }
    
    res.success = true;
    res.data = {
        processedCount: processedCount,
        newRecordsCount: newRecords.length,
        updateRecordsCount: updateRecords.length
    };
    res.msg = "批量处理记录完成，处理" + processedCount + "条，新增" + newRecords.length + "条，更新" + updateRecords.length + "条";
    
    // 记录处理信息
    logger.info("ProcessRecordList-批量处理完成，entype=" + entype + "，处理=" + processedCount + "，新增=" + newRecords.length + "，更新=" + updateRecords.length);
    
} catch (error) {
    res.success = false;
    var msg = "ProcessRecordList-批量处理记录失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="QueryExistingData" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryExistingData    {"category":"ext"}
 * @description   批量查询已存在数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    entype    文件类型编码
 * @param    {INFOTABLE}    sourceIds    源ID列表
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: {} };

try {
    // 参数验证
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    
    if (!sourceIds || !sourceIds.rows || sourceIds.rows.length === 0) {
        res.success = true;
        res.data = {};
        res.msg = "源ID列表为空";
        result = res;
        return;
    }
    
    // 构建IN子句的源ID列表
    var sourceIdList = [];
    for (var i = 0; i &lt; sourceIds.rows.length; i++) {
        var sourceId = sourceIds.rows[i].SOURCE_ID;
        if (sourceId &amp;&amp; sourceId !== "") {
            // SQL转义处理
            sourceId = String(sourceId).replace(/'/g, "''");
            sourceIdList.push("'" + sourceId + "'");
        }
    }
    
    if (sourceIdList.length === 0) {
        res.success = true;
        res.data = {};
        res.msg = "有效源ID列表为空";
        result = res;
        return;
    }
    
    // 构建批量查询SQL
    var sql = "select SOURCE_ID, RESULT_ID, PDF_RESULT_ID, EXCEL_RESULT_ID " +
              "from XMLDATA_" + entype + " " +
              "where SOURCE_ID in (" + sourceIdList.join(",") + ")";
    
    // 执行查询
    var queryResult = Things["Thing.DB.Oracle"].RunQuery({
        sql: sql
    });
    
    // 构建结果映射
    var existingDataMap = {};
    if (queryResult &amp;&amp; queryResult.rows) {
        for (var j = 0; j &lt; queryResult.rows.length; j++) {
            var row = queryResult.rows[j];
            existingDataMap[row.SOURCE_ID] = {
                sourceId: row.SOURCE_ID,
                resultId: row.RESULT_ID,
                pdfResultId: row.PDF_RESULT_ID,
                excelResultId: row.EXCEL_RESULT_ID
            };
        }
    }
    
    res.success = true;
    res.data = existingDataMap;
    res.msg = "批量查询已存在数据成功，查询" + sourceIdList.length + "条，找到" + Object.keys(existingDataMap).length + "条已存在记录";
    
    // 记录查询信息
    logger.info("QueryExistingData-批量查询成功，entype=" + entype + "，查询数=" + sourceIdList.length + "，存在数=" + Object.keys(existingDataMap).length);
    
} catch (error) {
    res.success = false;
    var msg = "QueryExistingData-批量查询已存在数据失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="QuerySyncData" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QuerySyncData    {"category":"ext"}
 * @description   批量查询待同步数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID    {"aspect.defaultValue":"0"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: [] };

try {
    // 构建查询SQL
    var sql = "select a.*,b.REFTREEID from alldatalistview a,DATA_PACKAGE b " +
              "where a.NODECODE=b.ID and a.gathering_method='自动采集' and a.collectstatus='1'";
    
    // 如果指定了treeid，添加过滤条件
    if (treeid !== 0) {
        sql += " and b.REFTREEID=" + treeid;
    }
    
    // 添加排序，确保处理顺序一致
    sql += " order by b.REFTREEID, a.NODECODE";
    
    // 执行查询
    var queryResult = Things['Thing.DB.Oracle'].RunQuery({
        sql: sql
    });
    
    // 检查查询结果
    if (queryResult &amp;&amp; queryResult.rows) {
        var dataList = [];
        for (var i = 0; i &lt; queryResult.rows.length; i++) {
            var row = queryResult.rows[i];
            dataList.push({
                nodeCode: row.NODECODE,
                refTreeId: row.REFTREEID,
                fileType: row.FILE_TYPE,
                tableType: row.TABLETYPE,
                deliveryState: row.DELIVERY_STATE,
                collectStatus: row.COLLECTSTATUS,
                gatheringMethod: row.GATHERING_METHOD
            });
        }
        
        res.success = true;
        res.data = dataList;
        res.msg = "查询待同步数据成功，共" + dataList.length + "条记录";
        
        // 记录查询信息
        logger.info("QuerySyncData-查询待同步数据成功，treeid=" + treeid + "，记录数=" + dataList.length);
        
    } else {
        res.success = true;
        res.data = [];
        res.msg = "未找到待同步数据";
    }
    
} catch (error) {
    res.success = false;
    var msg = "QuerySyncData-查询待同步数据失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="SqlEscape" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    SqlEscape    {"category":"ext"}
 * @description   SQL转义处理  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    value    需要转义的值
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (value === null || value === undefined) {
        res.success = true;
        res.data = {
            originalValue: value,
            escapedValue: ""
        };
        res.msg = "空值转义完成";
        result = res;
        return;
    }
    
    // 转换为字符串
    var stringValue = String(value);
    
    // SQL转义：将单引号替换为两个单引号
    var escapedValue = stringValue.replace(/'/g, "''");
    
    res.success = true;
    res.data = {
        originalValue: stringValue,
        escapedValue: escapedValue
    };
    res.msg = "SQL转义完成";
    
} catch (error) {
    res.success = false;
    var msg = "SqlEscape-SQL转义失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation name="UpdateStatusBatch" description="" handlerName="Script"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateStatusBatch    {"category":"ext"}
 * @description   批量更新数据状态为old  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID    {"aspect.defaultValue":"0"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 定义需要更新的表名数组
    var tables = [
        'DESIGN_DATA_RESULT',
        'CRAFT_DATA_RESULT',
        'PROCESS_CONTROL_RESULT',
        'QUALITY_CONTROL_RESULT'
    ];
    
    var updateResults = [];
    var totalUpdated = 0;
    
    // 批量构建和执行更新语句
    for (var i = 0; i &lt; tables.length; i++) {
        var table = tables[i];
        var sql = "";
        
        try {
            if (treeid !== 0) {
                // 更新指定节点的数据
                sql = "update " + table + " set STATUS='old' " +
                      "where NODECODE in (select id from DATA_PACKAGE where REFTREEID=" + treeid + ") " +
                      "and STATUS != 'old'";
            } else {
                // 更新所有数据
                sql = "update " + table + " set STATUS='old' where STATUS != 'old'";
            }
            
            // 执行更新
            var updateResult = Things['Thing.DB.Oracle'].RunCommand({
                sql: sql
            });
            
            updateResults.push({
                table: table,
                success: true,
                sql: sql,
                result: updateResult
            });
            
            // 记录更新信息
            logger.info("UpdateStatusBatch-表更新成功：" + table + "，treeid=" + treeid);
            
        } catch (tableError) {
            updateResults.push({
                table: table,
                success: false,
                sql: sql,
                error: String(tableError)
            });
            
            logger.error("UpdateStatusBatch-表更新失败：" + table + "，错误：" + tableError);
        }
    }
    
    // 统计成功更新的表数量
    var successCount = 0;
    for (var j = 0; j &lt; updateResults.length; j++) {
        if (updateResults[j].success) {
            successCount++;
        }
    }
    
    res.success = successCount &gt; 0;
    res.data = {
        totalTables: tables.length,
        successCount: successCount,
        failureCount: tables.length - successCount,
        updateResults: updateResults
    };
    
    if (successCount === tables.length) {
        res.msg = "批量更新状态完成，成功更新" + successCount + "个表";
    } else if (successCount &gt; 0) {
        res.msg = "批量更新状态部分成功，成功更新" + successCount + "个表，失败" + (tables.length - successCount) + "个表";
    } else {
        res.msg = "批量更新状态失败，所有表更新都失败";
    }
    
    // 记录操作信息
    logger.info("UpdateStatusBatch-批量更新完成，treeid=" + treeid + "，成功=" + successCount + "，总计=" + tables.length);
    
} catch (error) {
    res.success = false;
    var msg = "UpdateStatusBatch-批量更新状态失败，原因：" + error;
    logger.error(msg + "，treeid=" + treeid);
    res.msg = msg;
}

result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation></ServiceImplementations><Subscriptions/></ThingShape><PropertyBindings/><RemotePropertyBindings/><RemoteServiceBindings/><RemoteEventBindings/><AlertConfigurations/><ImplementedShapes/><ThingProperties/></Thing></Things></Entities>