/**
 * @definition    BatchInsertResults    {"category":"ext"}
 * @description   批量插入结果数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    tableType    表类型
 * @param    {INFOTABLE}    resultData    结果数据列表
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: [] };

try {
    // 参数验证
    if (!tableType || tableType === "") {
        throw "表类型不能为空";
    }
    if (!resultData || !resultData.rows || resultData.rows.length === 0) {
        res.success = true;
        res.data = [];
        res.msg = "结果数据列表为空";
        result = res;
        return;
    }
    
    var insertedIds = [];
    var seqName = "";
    
    // 根据表类型确定序列名
    if (tableType === "DESIGN_DATA_RESULT") {
        seqName = "design_cm_sequence.nextval";
    } else if (tableType === "CRAFT_DATA_RESULT") {
        seqName = "craft_cm_sequence.nextval";
    } else if (tableType === "PROCESS_CONTROL_RESULT") {
        seqName = "process_cm_sequence.nextval";
    } else if (tableType === "QUALITY_CONTROL_RESULT") {
        seqName = "quality_cm_sequence.nextval";
    } else {
        throw "不支持的表类型：" + tableType;
    }
    
    // 获取当前时间
    var nowTime = me.GetCurrentTimeString();
    if (!nowTime.success) {
        throw "获取当前时间失败：" + nowTime.msg;
    }
    var timeStr = nowTime.data.timeString;
    
    // 批量构建INSERT语句
    var insertSqls = [];
    
    for (var i = 0; i < resultData.rows.length; i++) {
        var row = resultData.rows[i];
        
        // SQL转义处理
        var nodeCode = me.SqlEscape({ value: row.NODECODE || "" }).data.escapedValue;
        var nodeName = me.SqlEscape({ value: row.NODENAME || "" }).data.escapedValue;
        var fileNumber = me.SqlEscape({ value: row.FILE_NUMBER || "" }).data.escapedValue;
        var fileName = me.SqlEscape({ value: row.FILE_NAME || "" }).data.escapedValue;
        var fileType = me.SqlEscape({ value: row.FILE_TYPE || "" }).data.escapedValue;
        var gatheringMethod = me.SqlEscape({ value: row.GATHERING_METHOD || "" }).data.escapedValue;
        var sourceSystem = me.SqlEscape({ value: row.SOURCE_SYSTEM || "" }).data.escapedValue;
        var deliveryState = me.SqlEscape({ value: row.DELIVERY_STATE || "" }).data.escapedValue;
        var securityLevel = me.SqlEscape({ value: row.SECURITY_LEVEL || "" }).data.escapedValue;
        var filePath = me.SqlEscape({ value: row.FILEPATH || "" }).data.escapedValue;
        var filename = me.SqlEscape({ value: row.FILENAME || "" }).data.escapedValue;
        var stateCheck = me.SqlEscape({ value: row.STATE_CHECK || "" }).data.escapedValue;
        var productId = me.SqlEscape({ value: row.PRODUCT_ID || "" }).data.escapedValue;
        var status = me.SqlEscape({ value: row.STATUS || "" }).data.escapedValue;
        var fileFormat = me.SqlEscape({ value: row.FILE_FORMAT || "" }).data.escapedValue;
        var docInfo = me.SqlEscape({ value: row.DOC_INFO || "" }).data.escapedValue;
        
        var insertSql = "insert into " + tableType + 
            " (ID, NODECODE, NODENAME, FILE_NUMBER, FILE_NAME, FILE_TYPE, " +
            "GATHERING_METHOD, SOURCE_SYSTEM, DELIVERY_STATE, SECURITY_LEVEL, " +
            "FILEPATH, FILENAME, STATE_CHECK, PRODUCT_ID, STATUS, FILE_FORMAT, " +
            "DOC_INFO, CREATE_TIMESTAMP, CREATOR) values (" +
            seqName + ", '" + nodeCode + "', '" + nodeName + "', '" + fileNumber + "', '" +
            fileName + "', '" + fileType + "', '" + gatheringMethod + "', '" +
            sourceSystem + "', '" + deliveryState + "', '" + securityLevel + "', '" +
            filePath + "', '" + filename + "', '" + stateCheck + "', '" +
            productId + "', '" + status + "', '" + fileFormat + "', '" +
            docInfo + "', '" + timeStr + "', 'wanghq')";
        
        insertSqls.push(insertSql);
    }
    
    // 批量执行插入
    for (var j = 0; j < insertSqls.length; j++) {
        try {
            Things['Thing.DB.Oracle'].RunCommand({
                sql: insertSqls[j]
            });
            insertedIds.push(j + 1); // 记录成功插入的索引
        } catch (insertError) {
            logger.error("BatchInsertResults-插入失败，索引=" + j + "，错误：" + insertError);
        }
    }
    
    res.success = true;
    res.data = insertedIds;
    res.msg = "批量插入完成，成功插入" + insertedIds.length + "条，总计" + insertSqls.length + "条";
    
    // 记录操作信息
    logger.info("BatchInsertResults-批量插入完成，表=" + tableType + "，成功=" + insertedIds.length + "，总计=" + insertSqls.length);
    
} catch (error) {
    res.success = false;
    var msg = "BatchInsertResults-批量插入失败，原因：" + error;
    logger.error(msg + "，表=" + tableType);
    res.msg = msg;
}

result = res;
