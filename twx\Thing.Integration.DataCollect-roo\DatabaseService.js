/**
 * @definition    DatabaseService    {"category":"ext"}
 * @description   数据库操作服务，提供安全的SQL查询、插入、更新、删除操作  wanghq 2025年8月21日19:30:30
 * @implementation    {Script}
 *
 * @param    {STRING}    operation    操作类型：query/insert/update/delete
 * @param    {STRING}    sql    SQL语句
 * @param    {JSON}    params    SQL参数（可选，用于防止SQL注入）
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: null
};

try {
    var operation = operation || "";
    var sql = sql || "";

    if (operation === "" || sql === "") {
        throw "操作类型和SQL语句不能为空";
    }

    // SQL安全转义函数
    function sqlEscape(value) {
        if (value === null || value === undefined) {
            return "";
        }
        return String(value).replace(/'/g, "''");
    }

    // 构建安全的SQL语句
    if (params && typeof params === "object") {
        for (var key in params) {
            if (params.hasOwnProperty(key)) {
                var placeholder = ":" + key;
                var escapedValue = sqlEscape(params[key]);
                sql = sql.replace(new RegExp(placeholder, 'g'), "'" + escapedValue + "'");
            }
        }
    }

    logger.info("DatabaseService-" + operation + "-执行SQL：" + sql);

    var dbResult;
    switch (operation.toLowerCase()) {
        case "query":
            dbResult = Things['Thing.DB.Oracle'].RunQuery({
                sql: sql
            });
            result.data = dbResult.rows;
            result.success = true;
            result.msg = "查询成功";
            break;

        case "insert":
        case "update":
        case "delete":
            dbResult = Things['Thing.DB.Oracle'].RunCommand({
                sql: sql
            });
            result.data = dbResult;
            result.success = true;
            result.msg = "操作成功";
            break;

        default:
            throw "不支持的操作类型：" + operation;
    }

} catch (error) {
    result.success = false;
    var msg = "DatabaseService-数据库操作失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;