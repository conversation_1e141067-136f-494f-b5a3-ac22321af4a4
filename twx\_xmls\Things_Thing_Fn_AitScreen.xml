<?xml version="1.0" encoding="UTF-8" standalone="yes"?><Entities build="b68" majorVersion="8" minorVersion="3" modelPersistenceProviderPackage="PostgresPersistenceProviderPackage" revision="9" schemaVersion="1032" universal=""><Things><Thing description="ait质量数据大屏" documentationContent="" effectiveThingPackage="ConfiguredThing" enabled="true" homeMashup="" identifier="" lastModifiedDate="2025-08-21T10:17:36.961+08:00" name="Thing.Fn.AitScreen" projectName="" published="false" tags="" thingTemplate="GenericThing" valueStream=""><avatar/><DesignTimePermissions><Create/><Read/><Update/><Delete/><Metadata/></DesignTimePermissions><RunTimePermissions/><VisibilityPermissions><Visibility/></VisibilityPermissions><ConfigurationTableDefinitions/><ConfigurationTables/><ThingShape><PropertyDefinitions><PropertyDefinition aspect.dataChangeType="VALUE" aspect.defaultValue="25" aspect.isPersistent="true" baseType="INTEGER" category="" description="" isLocalOnly="false" name="listBarWidth" ordinal="4"/><PropertyDefinition aspect.dataChangeType="VALUE" aspect.defaultValue="25" aspect.isPersistent="true" baseType="INTEGER" category="" description="" isLocalOnly="false" name="problemBarWidth" ordinal="3"/><PropertyDefinition aspect.dataChangeType="VALUE" aspect.defaultValue="25" aspect.isPersistent="true" baseType="INTEGER" category="" description="" isLocalOnly="false" name="submitBarWidth" ordinal="5"/></PropertyDefinitions><ServiceDefinitions><ServiceDefinition aspect.isAsync="false" category="" description="生成现场问题处理单数据 wanghq 2024年1月12日10:32:09" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CreateProblemData"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="生成随机数据 wanghq 2023年3月29日21:43:47" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CreateRandomData"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="fileType" description="" ordinal="0" aspect.defaultValue="现场问题处理单"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="生成提交单的随机数据 wanghq 2023年4月1日22:33:00" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="CreateRandomSubmitData"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取技术状态更改单图表选项 wanghq 2025年5月22日11:00:00" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetChangeOrderChartOption"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取柱状图的数据缩放区域参数 datetime 2024年1月23日15:41:06" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetChartDataZoom"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取清单书柱状图的柱状图option datetime 2024年1月23日14:09:55" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetListChartOption"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取不合格品审理单图表选项 wanghq 2025年5月23日" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetNonconformityChartOption"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="datetime 2024年1月12日16:23:52" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetProblemChartOption"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取现场问题处理单的更改工艺文件和更改设计文件的数量sql datetime 2024年1月15日10:27:34" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetProblemTypeCountSql"><ResultType baseType="STRING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="childType" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="3"/><FieldDefinition baseType="STRING" name="state" description="" ordinal="4"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取现场问题处理单的更改工艺文件和更改设计文件的sql datetime 2024年1月15日14:02:09" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetProblemTypeDataSql"><ResultType baseType="STRING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="childType" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="3"/><FieldDefinition baseType="STRING" name="state" description="" ordinal="4"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取查询数量的sql wanghq 2023年3月30日18:20:32" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetQueryCountSql"><ResultType baseType="STRING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="fileType" description="" ordinal="3" aspect.defaultValue="现场问题处理单"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取所有严重程度 wanghq 2025年6月4日09:30:40" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetSeverityLevels"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取所有情况类型 wanghq 2025年5月23日10:00:00" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetSituationTypes"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取产品提交单的柱状图option wanghq 2023年4月17日09:29:19" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetSubmitChartOption"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="JSON" name="itemCounts" description="" ordinal="0"/><FieldDefinition baseType="JSON" name="itemNames" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取查询产品交接单的sql wanghq 2023年4月2日00:11:15" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetSubmitListSql"><ResultType baseType="STRING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取现场临时处理单的柱状图option datetime 2024年11月7日19:27:15" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetTempChartOption"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="INTEGER" name="categorysLen" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取用户的数据标签 wanghq 2023年3月30日09:26:01" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetUserDataTag"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="username" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取用户的角色关联的数据标签的型号 wanghq 2023年4月13日10:28:19" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetUserRoleModel"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="username" description="" ordinal="0"/><FieldDefinition baseType="INTEGER" name="isUseScreen" description="" ordinal="1" aspect.defaultValue="0"/><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="2" aspect.defaultValue="-1.0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取查询的sql wanghq 2023年3月30日18:08:59" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="GetWhereSql"><ResultType baseType="STRING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="fileType" description="" ordinal="3" aspect.defaultValue="现场问题处理单"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="获取所有的型号 wanghq 2023年4月13日10:38:45" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryAllModel"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="username" description="" ordinal="0"/><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="1" aspect.defaultValue="-1.0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询型号进度 wanghq 2024年5月21日14:21:03" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryAllModelProcess"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询技术状态更改单分支详情 wanghq 2025年5月28日21:28:24" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryChangeBranchDetail"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="bindId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="branchFz" description="分支编号，如果提供则只查询该分支" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询技术状态更改单的数量 wanghq 2025年5月22日10:45:00" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryChangeOrderCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="username" description="" ordinal="3" aspect.defaultValue=" "/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询技术状态更改单列表 wanghq 2025年5月22日11:15:00" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryChangeOrderList"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="3" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="status" description="" ordinal="4" aspect.defaultValue="all"/><FieldDefinition baseType="STRING" name="situation" description="" ordinal="5" aspect.defaultValue="all"/><FieldDefinition baseType="STRING" name="queryType" description="" ordinal="6" aspect.defaultValue="all"/><FieldDefinition baseType="NUMBER" name="page" description="" ordinal="7" aspect.defaultValue="1"/><FieldDefinition baseType="NUMBER" name="limit" description="" ordinal="8" aspect.defaultValue="10"/><FieldDefinition baseType="BOOLEAN" name="isAllData" description="" ordinal="9" aspect.defaultValue="false"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="范燚指定的统计 wanghq 2024年10月24日14:16:12" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryFanyi"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询清单列表 wanghq 2023年3月30日23:04:04" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryList"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="fileType" description="" ordinal="3" aspect.defaultValue="现场问题处理单"/><FieldDefinition baseType="NUMBER" name="page" description="" ordinal="4" aspect.defaultValue="1.0"/><FieldDefinition baseType="NUMBER" name="limit" description="" ordinal="5" aspect.defaultValue="10.0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询型号的单据数量 wanghq 2023年4月13日10:48:54" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryModelListCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="username" description="" ordinal="3" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="fileType" description="" ordinal="4" aspect.defaultValue="现场问题处理单"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询型号阶段的AIT过程节点以及型号阶段名称 datetime 2024年1月10日18:39:43" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryModelPhaseAIT"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询型号进度 wanghq 2024年1月10日19:15:58" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryModelProcess"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="从Mes查询查询型号进度 wanghq 2025年4月8日21:32:47" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryModelProcessFromMes"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询产品交接单的数量 wanghq 2023年4月2日00:02:30" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryModelSubmitCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="username" description="" ordinal="3" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="groupType" description="" ordinal="4" aspect.defaultValue="ISCERTIFICATE"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询不合格品审理单的数量 wanghq 2025年5月23日" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryNonconformityCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="username" description="" ordinal="3" aspect.defaultValue=" "/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询不合格品审理单列表 wanghq 2025年5月23日" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryNonconformityList"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="username" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="3" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="status" description="" ordinal="4" aspect.defaultValue="all"/><FieldDefinition baseType="STRING" name="severityLevel" description="" ordinal="5" aspect.defaultValue="all"/><FieldDefinition baseType="NUMBER" name="page" description="" ordinal="6" aspect.defaultValue="1.0"/><FieldDefinition baseType="NUMBER" name="limit" description="" ordinal="7" aspect.defaultValue="10.0"/><FieldDefinition baseType="BOOLEAN" name="isAllData" description="" ordinal="8" aspect.defaultValue="false"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询现场问题处理单的数量 wanghq 2024年1月12日15:26:09" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryProblemCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="username" description="" ordinal="3" aspect.defaultValue=" "/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询过程节点的数量 wanghq 2023年3月30日16:37:56" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryProccessCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="modelId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="fileType" description="" ordinal="3" aspect.defaultValue="现场问题处理单"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询质量信息看板数据    wanghq    2025年8月5日18:19:12" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryQualityInfo"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询产品交接单的数量 wanghq 2023年4月2日00:02:30" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QuerySubmitCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="username" description="" ordinal="3" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="groupType" description="" ordinal="4" aspect.defaultValue="ISCERTIFICATE"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询产品交接单列表 wanghq 2023年3月30日23:04:04" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QuerySubmitList"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="groupType" description="" ordinal="3"/><FieldDefinition baseType="INTEGER" name="page" description="" ordinal="4" aspect.defaultValue="1"/><FieldDefinition baseType="INTEGER" name="limit" description="" ordinal="5" aspect.defaultValue="10"/><FieldDefinition baseType="STRING" name="unit" description="" ordinal="6" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="seriesName" description="" ordinal="7"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询产品交接单的提交单位的数量 wanghq 2023年4月2日00:02:30" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QuerySubmitUnitCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="groupType" description="" ordinal="3" aspect.defaultValue="ISCERTIFICATE"/><FieldDefinition baseType="STRING" name="seriesName" description="" ordinal="4"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询临时处理单清单列表 wanghq 2024年11月8日10:13:28" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTempList"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1"/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2"/><FieldDefinition baseType="STRING" name="childType" description="" ordinal="3"/><FieldDefinition baseType="INTEGER" name="page" description="" ordinal="4" aspect.defaultValue="1"/><FieldDefinition baseType="INTEGER" name="limit" description="" ordinal="5" aspect.defaultValue="10"/><FieldDefinition baseType="STRING" name="status" description="" ordinal="6"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询现场临时处理单的数量 wanghq 2024年11月7日18:38:30" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryTemporaryCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/><FieldDefinition baseType="STRING" name="startDate" description="" ordinal="1" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="endDate" description="" ordinal="2" aspect.defaultValue=" "/><FieldDefinition baseType="STRING" name="username" description="" ordinal="3" aspect.defaultValue=" "/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="查询更新时间 wanghq 2023年4月19日14:25:31" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="QueryUpdateTime"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="手动设置的当前阶段型号的AIT过程节点名称 datetime 2024年1月10日19:03:18" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="SetModelPhaseCurrentAITNode"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="aitNode" description="" ordinal="1"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="判断字符串是否为空 datetime 2024年1月12日16:29:43" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="StrIsEmpty"><ResultType baseType="BOOLEAN" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="STRING" name="str" description="" ordinal="0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="同步MES型号进度 wanghq 2025年4月17日16:22:26" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="SyncMESModelProgress"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="wanghq 2025年5月21日14:15:0" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateAllData"><ResultType baseType="NOTHING" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="更新技术状态更改单的统计数据 wanghq 2025年5月22日10:30:00" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateChangeOrderCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="更新型号的相关数据统计信息 wanghq 2023年4月13日14:11:19" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateModelStatistics"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="modelId" description="" ordinal="0"/><FieldDefinition baseType="STRING" name="statType" description="" ordinal="1"/><FieldDefinition baseType="NUMBER" name="statCount" description="" ordinal="2"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="更新型号的产品交接单数量 wanghq 2023年4月13日15:48:56" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateModelSubmitCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="更新型号的清单数量 wanghq 2023年4月13日14:28:14" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateModelTypeListCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/></ParameterDefinitions></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="更新不合格品审理单的统计数据 wanghq 2025年5月23日" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateNonconformityCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions/></ServiceDefinition><ServiceDefinition aspect.isAsync="false" category="" description="更新现场问题处理单中的更改设计文件和更改工艺文件 datetime 2024年1月12日14:16:16" isAllowOverride="false" isLocalOnly="false" isOpen="false" isPrivate="false" name="UpdateProblemCount"><ResultType baseType="JSON" description="" name="result" ordinal="0"/><ParameterDefinitions><FieldDefinition baseType="NUMBER" name="treeId" description="" ordinal="0" aspect.defaultValue="-1.0"/></ParameterDefinitions></ServiceDefinition></ServiceDefinitions><EventDefinitions/><ServiceMappings/><ServiceImplementations><ServiceImplementation description="" handlerName="Script" name="CreateProblemData"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    CreateProblemData
 * @description   生成现场问题处理单数据 wanghq 2024年1月12日10:32:09
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {

    //清空之前随机生成的现场问题处理单数据
    Things['Thing.DB.Oracle'].RunCommand({ sql: "delete from PROCESS_CONTROL_RESULT where FILE_TYPE='现场问题处理单' and GATHERING_METHOD='自动采集'" });
    Things['Thing.DB.Oracle'].RunCommand({ sql: "delete from XMLDATA_TECHPROBLEM" });


    var total = 0;
    var startTime = new Date().getTime();

    //随机生成10位数的文件编号
    function generateFileId() {
        var fileId = "";
        for (var i = 0; i &lt; 10; i++) {
            fileId += Math.floor(Math.random() * 10);
        }
        return fileId;
    }

    //随机生成文件名称
    function generateFileName() {
        var characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"; // 定义字符集
        var fileName = "";
        for (var i = 0; i &lt; 8; i++) { // 文件名长度为8位
            fileName += characters.charAt(Math.floor(Math.random() * characters.length)); // 从字符集中随机选择一个字符
        }
        return fileName;
    }

    //随机生成时间
    function generateRandomTime() {
        var startTimestamp = parseDate('2018-01-01 00:00:00', "yyyy-MM-dd HH:mm:ss").getTime(); // 开始时间的时间戳
        var endTimestamp = parseDate('2024-1-20 23:59:59', "yyyy-MM-dd HH:mm:ss").getTime(); // 结束时间的时间戳
        var randomTimestamp = Math.floor(Math.random() * (endTimestamp - startTimestamp + 1) + startTimestamp); // 随机时间的时间戳
        return dateFormat(new Date(randomTimestamp), "yyyy-MM-dd HH:mm:ss"); // 返回随机时间的 Date 对象
    }

    function generateIndex(arrLen) {
        var randomInt = Math.floor(Math.random() * arrLen);
        var arrIndex = randomInt === arrLen ? 0 : randomInt;
        return arrIndex;
    }

    var isNeeds = ["0", "1", "0", "1"];

    //查询所有的AIT过程节点
    var nodeSql = "select id from DATA_PACKAGE where REFTREEID in ( SELECT TREEID FROM DATAPACKAGETREE WHERE NODENAME IN ( SELECT NODENAME FROM DATAPACKAGETREE WHERE PARENTID=6))";
    var nodeRs = Things['Thing.DB.Oracle'].RunQuery({ sql: nodeSql });

    // 生成16位数字字符串
    function generate16DigitNumber() {
        var num = '';
        for (var i = 0; i &lt; 16; i++) {
            num += Math.floor(Math.random() * 10);
        }
        return num;
    }
    // 随机返回空字符串或16位数字字符串
    function getRandomChangeNo() {
        return Math.random() &lt; 0.5 ? '' : generate16DigitNumber();
    }

    // 生成0~9的随机整数
    function generateRandomState() {
        return Math.floor(Math.random() * 10);
    }

    for (var i = 0; i &lt; nodeRs.rows.length; i++) {
        var nodecode = nodeRs.rows[i]["ID"];
        var count = Math.floor(Math.random() * 9 + 10);
        for (j = 0; j &lt; count; j++) {
            var fileNumber = generateFileId();
            var fileName = generateFileName();
            var createTime = generateRandomTime();
            var resultId = Things["Thing.DB.Oracle"].RunQuery({ sql: "select PROCESS_CM_SEQUENCE.nextval AS NEXTVAL from dual" }).getRow(0).NEXTVAL;
            var insertSql = "insert into PROCESS_CONTROL_RESULT (ID, NODECODE, NODENAME, FILE_TYPE, FILE_NUMBER, FILE_NAME, GATHERING_METHOD,\
                SOURCE_SYSTEM, DELIVERY_STATE, SECURITY_LEVEL, STATE_CHECK, FILEPATH,\
                CREATE_TIMESTAMP, CREATOR, FILE_FORMAT, FILENAME, STATUS)\
                values ("+ resultId + "," + nodecode + "," + nodecode + ",'现场问题处理单','" + fileNumber + "','" + fileName + "','自动采集',\
                'MES','提交','1','未确认','//2021-08//c2f484c4-dd70-4877-9a8e-a763d4b74e69',\
                '"+ createTime + "','adm','pdf','" + fileName + "','old')";
            Things['Thing.DB.Oracle'].RunCommand({ sql: insertSql });

            var isNeedChangeTechFile = isNeeds[generateIndex(isNeeds.length)];
            var isNeedChangeDesignFile = isNeeds[generateIndex(isNeeds.length)];
            var isNeedDeviateTechFile = isNeeds[generateIndex(isNeeds.length)];
            var isNeedDeviateDesignFile = isNeeds[generateIndex(isNeeds.length)];
            var processChangeNo = getRandomChangeNo();
            var designChangeNo = getRandomChangeNo();
            var randomState = generateRandomState();
            var insertXmlDatasql = "insert into XMLDATA_TECHPROBLEM" +
                "(id, source_id,result_id, billcode, department, content, proposer, proposetime," +
                "state, reasontype, reason, scheme, disposer, disposetime, implementtech," +
                "implementor, implementtime, firstjoint, firstjointtime, secondjoint," +
                "secondjointtime, thirdjoint, thirdjointtime, forthjoint, forthjointtime," +
                "adfromtechforcontent, adfromtechforreason, adfromtechforscheme, adfromtechfortech," +
                "adfromchanbaoforcontent, adfromchanbaoforreason, adfromchanbaoforscheme," +
                "adfromchanbaofortech, adfrommaintechforcontent, adfrommaintechforreason," +
                "adfrommaintechforscheme, adfrommaintechfortech, isneedchanbaosign, isneedmaintechsign," +
                "isneedchangetechfile, techcontrol, techcontroltime, isneedchangedesignfile,isneeddeviatetechfile,isneeddeviatedesignfile,chanbao," +
                "chanbaotime, maintech, maintechtime, downloadurl, filename,product_id, filepath, PROCESS_CHANGE_NO, DESIGN_CHANGE_NO)" +
                "values" +
                "(XML_CONTENT_TECHPROBLEM.nextval, " +
                "'" + (resultId + '_PROCESS_CONTROL_RESULT') + "'," +
                "" + resultId + "," +
                "'BillCode '," +
                "'Department '," +
                "'Content '," +
                "'Proposer '," +
                "'ProposeTime '," +
                "'" + randomState + "'," +
                "'ReasonType '," +
                "'Reason '," +
                "'Scheme '," +
                "'Disposer '," +
                "'DisposeTime '," +
                "'ImplementTech '," +
                "'Implementor '," +
                "'ImplementTime '," +
                "'FirstJoint '," +
                "'FirstJointTime '," +
                "'SecondJoint '," +
                "'SecondJointTime '," +
                "'ThirdJoint '," +
                "'ThirdJointTime '," +
                "'ForthJoint '," +
                "'ForthJointTime '," +
                "'AdFromTechForContent '," +
                "'AdFromTechForReason '," +
                "'AdFromTechForScheme '," +
                "'AdFromTechForTech '," +
                "'AdFromChanbaoForContent '," +
                "'AdFromChanbaoForReason '," +
                "'AdFromChanbaoForScheme '," +
                "'AdFromChanbaoForTech '," +
                "'AdFromMainTechForContent '," +
                "'AdFromMainTechForReason '," +
                "'AdFromMainTechForScheme '," +
                "'AdFromMainTechForTech '," +
                "'IsNeedChanbaoSign '," +
                "'IsNeedMainTechSign '," +
                "'" + isNeedChangeTechFile + "'," +
                "'TechControl '," +
                "'TechControlTime '," +
                "'" + isNeedChangeDesignFile + "'," +
                "'" + isNeedDeviateTechFile + "'," +
                "'" + isNeedDeviateDesignFile + "'," +
                "'Chanbao '," +
                "'ChanbaoTime '," +
                "'MainTech '," +
                "'MainTechTime '," +
                "'DownLoadURL '," +
                "' fileName '," +
                "'productId '," +
                "'filePath '," +
                "'" + processChangeNo + "'," +
                "'" + designChangeNo + "')";
            Things['Thing.DB.Oracle'].RunCommand({ sql: insertXmlDatasql });
            total++;
        }
    }
    var endTime = new Date().getTime();
    res.success = true;
    res.msg = "成功生成" + total + "条数据,耗时" + ((endTime - startTime) / 1000) + "秒";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="CreateRandomData"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    CreateRandomData
 * @description   生成随机数据 wanghq 2023年3月29日21:43:47
 * @implementation    {Script}
 *
 * @param    {STRING}    fileType        {"aspect.defaultValue":"现场问题处理单"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var total = 0;
    var startTime = new Date().getTime();

    //随机生成10位数的文件编号
    function generateFileId() {
        var fileId = "";
        for (var i = 0; i &lt; 10; i++) {
            fileId += Math.floor(Math.random() * 10);
        }
        return fileId;
    }

    //随机生成文件名称
    function generateFileName() {
        var characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"; // 定义字符集
        var fileName = "";
        for (var i = 0; i &lt; 8; i++) { // 文件名长度为8位
            fileName += characters.charAt(Math.floor(Math.random() * characters.length)); // 从字符集中随机选择一个字符
        }
        return fileName;
    }

    //随机生成时间
    function generateRandomTime() {
        var startTimestamp = parseDate('2018-01-01 00:00:00', "yyyy-MM-dd HH:mm:ss").getTime(); // 开始时间的时间戳
        var endTimestamp = parseDate('2023-12-31 23:59:59', "yyyy-MM-dd HH:mm:ss").getTime(); // 结束时间的时间戳
        var randomTimestamp = Math.floor(Math.random() * (endTimestamp - startTimestamp + 1) + startTimestamp); // 随机时间的时间戳
        return dateFormat(new Date(randomTimestamp), "yyyy-MM-dd HH:mm:ss"); // 返回随机时间的 Date 对象
    }

    //查询所有的AIT过程节点
    var nodeSql = "select id from DATA_PACKAGE where REFTREEID in ( SELECT TREEID FROM DATAPACKAGETREE WHERE NODENAME IN ( SELECT NODENAME FROM DATAPACKAGETREE WHERE PARENTID=6))";
    var nodeRs = Things['Thing.DB.Oracle'].RunQuery({ sql: nodeSql });

    for (var i = 0; i &lt; nodeRs.rows.length; i++) {
        var nodecode = nodeRs.rows[i]["ID"];
        var count = Math.floor(Math.random() * 9 + 10);
        for (j = 0; j &lt; count; j++) {
            var fileNumber = generateFileId();
            var fileName = generateFileName();
            var createTime = generateRandomTime();
            var insertSql = "insert into PROCESS_CONTROL_RESULT (ID, NODECODE, NODENAME, FILE_TYPE, FILE_NUMBER, FILE_NAME, GATHERING_METHOD,\
                SOURCE_SYSTEM, DELIVERY_STATE, SECURITY_LEVEL, STATE_CHECK, FILEPATH,\
                CREATE_TIMESTAMP, CREATOR, FILE_FORMAT, FILENAME, STATUS)\
                values (PROCESS_CM_SEQUENCE.nextval,"+ nodecode + "," + nodecode + ",'" + fileType + "','" + fileNumber + "','" + fileName + "','自动采集',\
                'MES','提交','1','未确认','//2021-08//c2f484c4-dd70-4877-9a8e-a763d4b74e69',\
                '"+ createTime + "','adm','pdf','" + fileName + "','old')";
            Things['Thing.DB.Oracle'].RunCommand({ sql: insertSql });
            total++;
        }
    }
    var endTime = new Date().getTime();
    res.success = true;
    res.msg = "成功生成" + total + "条数据,耗时" + ((endTime - startTime) / 1000) + "秒";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="CreateRandomSubmitData"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    CreateRandomSubmitData
 * @description   生成提交单的随机数据 wanghq 2023年4月1日22:33:00
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    
    //执行之前清空数据
    Things['Thing.DB.Oracle'].RunCommand({ sql: "delete from XMLDATA_PRODUCTSUBMIT" });
    Things['Thing.DB.Oracle'].RunCommand({ sql: "delete from PROCESS_CONTROL_RESULT where FILE_TYPE='产品交接单' and GATHERING_METHOD='自动采集' and filePath='//2021-08//c2f484c4-dd70-4877-9a8e-a763d4b74e69'" });

    var total = 0;
    var startTime = new Date().getTime();

    //随机生成10位数的文件编号
    function generateFileId() {
        var fileId = "";
        for (var i = 0; i &lt; 10; i++) {
            fileId += Math.floor(Math.random() * 10);
        }
        return fileId;
    }

    //随机生成文件名称
    function generateFileName() {
        var characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"; // 定义字符集
        var fileName = "";
        for (var i = 0; i &lt; 8; i++) { // 文件名长度为8位
            fileName += characters.charAt(Math.floor(Math.random() * characters.length)); // 从字符集中随机选择一个字符
        }
        return fileName + "提交单";
    }

    //随机生成时间
    function generateRandomTime() {
        var startTimestamp = parseDate('2018-01-01 00:00:00', "yyyy-MM-dd HH:mm:ss").getTime(); // 开始时间的时间戳
        var endTimestamp = parseDate('2023-12-31 23:59:59', "yyyy-MM-dd HH:mm:ss").getTime(); // 结束时间的时间戳
        var randomTimestamp = Math.floor(Math.random() * (endTimestamp - startTimestamp + 1) + startTimestamp); // 随机时间的时间戳
        return dateFormat(new Date(randomTimestamp), "yyyy-MM-dd HH:mm:ss"); // 返回随机时间的 Date 对象
    }

    function generateUUID() {
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
            var r = Math.random() * 16 | 0,
                v = c == 'x' ? r : (r &amp; 0x3 | 0x8);
            return v.toString(16);
        });
    }

    function generateIndex(arrLen) {
        var randomInt = Math.floor(Math.random() * arrLen);
        var arrIndex = randomInt === arrLen ? 0 : randomInt;
        return arrIndex;
    }

    // 生成随机证书状态：合格证概率1/5，无概率4/5
    function generateCertificateStatus() {
        var random = Math.random();
        return random &lt; 0.2 ? '合格证' : '无';
    }

    var fileNames = [];
    for (var i = 0; i &lt; 20; i++) {
        fileNames.push(generateFileName());
    }

    var fileNumbers = [];
    for (var i = 0; i &lt; 20; i++) {
        fileNumbers.push(generateFileId());
    }

    var batchCodes = [];
    for (var i = 0; i &lt; 20; i++) {
        batchCodes.push(generateFileId());
    }

    var units = ["812", "801", "802", "803", ""];
    var submits = ["未交付", "已交付", ""];
    var luohans = ["需要但未落焊", "不需落焊", "已落焊", ""];
    var luohanPhases = ["A阶段电测试改装", "B阶段电测试改装", "C阶段电测试改装", "热试验改装", "出厂改装"];
    //查询所有的AIT过程节点
    var nodeSql = "select id from DATA_PACKAGE where REFTREEID in ( SELECT TREEID FROM DATAPACKAGETREE WHERE NODENAME IN ( SELECT NODENAME FROM DATAPACKAGETREE WHERE PARENTID=6))";
    var nodeRs = Things['Thing.DB.Oracle'].RunQuery({ sql: nodeSql });

    var fileType = "产品交接单";
    var filePath = "//2021-08//c2f484c4-dd70-4877-9a8e-a763d4b74e69";

    for (var i = 0; i &lt; nodeRs.rows.length; i++) {
        var nodecode = nodeRs.rows[i]["ID"];
        var count = Math.floor(Math.random() * 10 + 2);
        for (j = 0; j &lt; count; j++) {
            var fileNumber = fileNumbers[generateIndex(fileNumbers.length)];
            var fileName = fileNames[generateIndex(fileNames.length)];
            var batchCode = batchCodes[generateIndex(batchCodes.length)];
            var createTime = generateRandomTime();
            var resultId = Things["Thing.DB.Oracle"].RunQuery({ sql: "select PROCESS_CM_SEQUENCE.nextval AS NEXTVAL from dual" }).getRow(0).NEXTVAL;
            var insertSql = "insert into PROCESS_CONTROL_RESULT (ID, NODECODE, NODENAME, FILE_TYPE, FILE_NUMBER, FILE_NAME, GATHERING_METHOD,\
                SOURCE_SYSTEM, DELIVERY_STATE, SECURITY_LEVEL, STATE_CHECK, FILEPATH,\
                CREATE_TIMESTAMP, CREATOR, FILE_FORMAT, FILENAME, STATUS)\
                values ("+ resultId + "," + nodecode + "," + nodecode + ",'" + fileType + "','" + fileNumber + "','" + fileName + "','自动采集',\
                'MES','提交','1','未确认','"+ filePath + "',\
                '"+ createTime + "','adm','pdf','" + fileName + "','old')";
            Things['Thing.DB.Oracle'].RunCommand({ sql: insertSql });

            var submitUnit = units[generateIndex(units.length)];
            var submit = submits[generateIndex(submits.length)];
            var luohan = luohans[generateIndex(luohans.length)];
            var luohanPhase = "";
            if (luohan == "需要但未落焊") {
                luohanPhase = luohanPhases[generateIndex(luohanPhases.length)];
            }

            // 新增：生成产品证明书数量和产品履历书数量，值为0、1中随机选取
            var certificateCount = Math.floor(Math.random() * 2);
            var resumeCount = Math.floor(Math.random() * 2);

            var insertXmlDatasql = "insert into xmldata_productsubmit" +
                "(id, source_id, result_id,ISCERTIFICATE,ISLUOHAN,LUOHANPHASE,billcode, productname, productcode, batchcode, location, packagingstate, identifypphysicalobjects, listinkind, codecorrectness, surfacestate, dimensionmarking, fasteningstate, inspectionofsocket, redundancyinspection, cableinspection, opticalmirrorstate, other, certificatenumber, resumenumber, productsubstance, contentsignature, completeinformation, storageperiod, qualifiedconclusion, completesignature, productresume, todoitems, temperatureandhumidity, informationnote, adfromsubmit, submitsignature, submitunit, totalunitsignature, adfromreceive, receivesignature, downloadurl, filename, filepath, other_certificate1, other_certificate2)" +
                "values" +
                "(xml_content_ProductSubmit.nextval, " +
                "'" + (resultId + '_PROCESS_CONTROL_RESULT') + "'," +
                resultId + "," +
                "'" + submit + "'," +
                "'" + luohan + "'," +
                "'" + luohanPhase + "'," +
                "'BillCode'," +
                "'" + fileName + "'," +
                "'" + fileNumber + "'," +
                "'" + batchCode + "'," +
                "'Location'," +
                "'PackagingState'," +
                "'IdentifyPphysicalObjects'," +
                "'ListInKind'," +
                "'CodeCorrectness'," +
                "'SurfaceState'," +
                "'DimensionMarking'," +
                "'FasteningState'," +
                "'InspectionOfSocket'," +
                "'RedundancyInspection'," +
                "'CableInspection'," +
                "'OpticalMirrorState'," +
                "'Other'," +
                certificateCount + "," +
                resumeCount + "," +
                "'ProductSubstance'," +
                "'ContentSignature'," +
                "'CompleteInformation'," +
                "'StoragePeriod'," +
                "'QualifiedConclusion'," +
                "'CompleteSignature'," +
                "'ProductResume'," +
                "'TodoItems'," +
                "'TemperatureAndHumidity'," +
                "'InformationNote'," +
                "'AdFromSubmit'," +
                "'SubmitSignature'," +
                "'" + submitUnit + "'," +
                "'TotalUnitSignature'," +
                "'AdFromReceive'," +
                "'ReceiveSignature'," +
                "'DownLoadURL'," +
                "'" + fileName + "'," +
                "'" + filePath + "'," +
                "'" + generateCertificateStatus() + "'," +
                "'" + generateCertificateStatus() + "')";
            Things['Thing.DB.Oracle'].RunCommand({ sql: insertXmlDatasql });
            total++;
        }
    }
    var endTime = new Date().getTime();
    res.success = true;
    res.msg = "成功生成" + total + "条数据,耗时" + ((endTime - startTime) / 1000) + "秒";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetChangeOrderChartOption"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    GetChangeOrderChartOption&#xD;
 * @description   获取技术状态更改单图表选项 wanghq 2025年5月22日11:00:00&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
// 技术状态更改单专用的dataZoom配置，限制最多显示3个型号&#xD;
var dataZoom = {&#xD;
    "type": "slider",&#xD;
    "show": true,&#xD;
    "showDetail": false,&#xD;
    "startValue": 0,&#xD;
    "endValue": 2,&#xD;
    "minValueSpan": 2,&#xD;
    "maxValueSpan": 2,&#xD;
    "bottom": 10,&#xD;
    "height": 10,&#xD;
    "backgroundColor": "rgba(47,69,84,0)",&#xD;
    "fillerColor": "#00cdff",&#xD;
    "selectedDataBackground": {&#xD;
        "areaStyle": "#00cdff"&#xD;
    },&#xD;
    "handleStyle": {&#xD;
        color: "#00cdff"&#xD;
    },&#xD;
    "moveHandleStyle": {&#xD;
        color: "#00cdff"&#xD;
    },&#xD;
    zoomLock: true&#xD;
};&#xD;
var res = {&#xD;
    "tooltip": {},&#xD;
    legend: {&#xD;
        top: 10,&#xD;
        itemGap: 15,&#xD;
        itemWidth: 18,&#xD;
        itemHeight: 12,&#xD;
        textStyle: {&#xD;
            color: "#fff",&#xD;
            fontSize: 12,&#xD;
            fontWeight: 'bold',&#xD;
            lineHeight: 16&#xD;
        }&#xD;
    },&#xD;
    "grid": {&#xD;
        "top": 80,&#xD;
        "bottom": 50,&#xD;
        "left": 50,&#xD;
        "right": 50&#xD;
    },&#xD;
    "xAxis": {&#xD;
        type: 'category',&#xD;
        axisLabel: {&#xD;
            rotate: 0&#xD;
        },&#xD;
        // 设置坐标轴的样式&#xD;
        axisLine: {&#xD;
            lineStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        }&#xD;
    },&#xD;
    "yAxis": {&#xD;
        minInterval: 1,&#xD;
        "show": true,&#xD;
        "axisLine": {&#xD;
            "lineStyle": {&#xD;
                "color": "white"&#xD;
            }&#xD;
        },&#xD;
        "axisLabel": {&#xD;
            "fontSize": 14&#xD;
        },&#xD;
        "type": "value",&#xD;
        "splitLine": {&#xD;
            "show": true,&#xD;
            "lineStyle": {&#xD;
                "color": "#141D54"&#xD;
            }&#xD;
        }&#xD;
    },&#xD;
    "dataZoom": [dataZoom],&#xD;
    dataset: {&#xD;
        source: []&#xD;
    },&#xD;
    // 添加默认的系列配置，后续会被覆盖&#xD;
    "series": [{&#xD;
        type: 'bar',&#xD;
        name: '总数量',&#xD;
        barWidth: 20,&#xD;
        showBackground: false,&#xD;
        label: {&#xD;
            show: true,&#xD;
            position: 'top',&#xD;
            textStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        },&#xD;
        itemStyle: {&#xD;
            color: {&#xD;
                type: 'linear',&#xD;
                x: 0,&#xD;
                y: 0,&#xD;
                x2: 0,&#xD;
                y2: 1,&#xD;
                colorStops: [{&#xD;
                    offset: 0,&#xD;
                    color: '#00CBD2' // 0% 处的颜色&#xD;
                }, {&#xD;
                    offset: 1,&#xD;
                    color: '#00A2FE' // 100% 处的颜色&#xD;
                }],&#xD;
                global: false&#xD;
            },&#xD;
            borderRadius: [20, 20, 0, 0]&#xD;
        }&#xD;
    }, {&#xD;
        type: 'bar',&#xD;
        name: '分支总数量',&#xD;
        barWidth: 20,&#xD;
        showBackground: false,&#xD;
        label: {&#xD;
            show: true,&#xD;
            position: 'top',&#xD;
            textStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        },&#xD;
        itemStyle: {&#xD;
            color: {&#xD;
                type: 'linear',&#xD;
                x: 0,&#xD;
                y: 0,&#xD;
                x2: 0,&#xD;
                y2: 1,&#xD;
                colorStops: [{&#xD;
                    offset: 0,&#xD;
                    color: '#99CB75' // 0% 处的颜色&#xD;
                }, {&#xD;
                    offset: 1,&#xD;
                    color: '#59d208' // 100% 处的颜色&#xD;
                }],&#xD;
                global: false&#xD;
            },&#xD;
            borderRadius: [20, 20, 0, 0]&#xD;
        }&#xD;
    }]&#xD;
};&#xD;
result = res;&#xD;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetChartDataZoom"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetChartDataZoom
 * @description   获取柱状图的数据缩放区域参数 datetime 2024年1月23日15:41:06
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {
    "type": "slider",
    "show": true,
    "showDetail": false,
    "startValue": 0,
    "endValue": 5,
    "minValueSpan": 9,
    "maxValueSpan": 9,
    "bottom": 10,
    "height": 10,
    "backgroundColor": "rgba(47,69,84,0)",
    "fillerColor": "#00cdff",
    "selectedDataBackground": {
        "areaStyle": "#00cdff"
    },
    "handleStyle": {
        color: "#00cdff"
    },
    "moveHandleStyle": {
        color: "#00cdff"
    },
    zoomLock: true
};
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetListChartOption"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetListChartOption
 * @description   获取清单书柱状图的柱状图option datetime 2024年1月23日14:09:55
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var dataZoom = me.GetChartDataZoom();
var res = {
    tooltip: {

    },
    grid: {
        top: 30,
        bottom: 80
    },
    xAxis: {
        type: 'category',
        data: [],
        axisLabel: {
            rotate: 30
        },
        // 设置坐标轴的样式
        axisLine: {
            lineStyle: {
                color: 'white'
            }
        }
    },
    yAxis: {
        show: true,
        minInterval: 1,
        // name: "数量",
        // nameRotate: 90,
        // nameGap: 60,
        // nameLocation: 'center',
        // nameTextStyle: {
        // 	fontSize: 14,
        // 	color: "white"
        // },
        // 设置坐标轴的样式
        axisLine: {
            lineStyle: {
                color: 'white'
            }
        },
        axisLabel: {
            fontSize: 14
        },
        type: 'value',
        splitLine: {
            show: true,
            lineStyle: {
                color: "#141D54"
            }
        }
    },
    dataZoom: [dataZoom],
    series: [{
        data: [],
        type: 'bar',
        showBackground: false,
        backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
        },
        label: {
            show: true,
            position: 'top',
            textStyle: {
                color: 'white'
            }
        },
        itemStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0,
                    color: '#00CBD2' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#00A2FE' // 100% 处的颜色
                }],
                global: false // 缺省为 false
            },
            borderRadius: [20, 20, 0, 0]
        },
        barWidth: me.listBarWidth
    }]
};
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetNonconformityChartOption"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    GetNonconformityChartOption&#xD;
 * @description   获取不合格品审理单图表选项 wanghq 2025年5月23日&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
// 不合格品审理单专用的dataZoom配置，限制最多显示3个型号&#xD;
var dataZoom = {&#xD;
    "type": "slider",&#xD;
    "show": true,&#xD;
    "showDetail": false,&#xD;
    "startValue": 0,&#xD;
    "endValue": 4,&#xD;
    "minValueSpan": 3,&#xD;
    "maxValueSpan": 3,&#xD;
    "bottom": 10,&#xD;
    "height": 10,&#xD;
    "backgroundColor": "rgba(47,69,84,0)",&#xD;
    "fillerColor": "#00cdff",&#xD;
    "selectedDataBackground": {&#xD;
        "areaStyle": "#00cdff"&#xD;
    },&#xD;
    "handleStyle": {&#xD;
        color: "#00cdff"&#xD;
    },&#xD;
    "moveHandleStyle": {&#xD;
        color: "#00cdff"&#xD;
    },&#xD;
    zoomLock: true&#xD;
};&#xD;
var res = {&#xD;
    "tooltip": {},&#xD;
    legend: {&#xD;
        top: 10,&#xD;
        itemGap: 15,&#xD;
        itemWidth: 18,&#xD;
        itemHeight: 12,&#xD;
        textStyle: {&#xD;
            color: "#fff",&#xD;
            fontSize: 12,&#xD;
            fontWeight: 'bold',&#xD;
            lineHeight: 16&#xD;
        }&#xD;
    },&#xD;
    "grid": {&#xD;
        "top": 80,&#xD;
        "bottom": 50,&#xD;
        "left": 50,&#xD;
        "right": 50&#xD;
    },&#xD;
    "xAxis": {&#xD;
        type: 'category',&#xD;
        axisLabel: {&#xD;
            rotate: 0&#xD;
        },&#xD;
        // 设置坐标轴的样式&#xD;
        axisLine: {&#xD;
            lineStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        }&#xD;
    },&#xD;
    "yAxis": {&#xD;
        minInterval: 1,&#xD;
        "show": true,&#xD;
        "axisLine": {&#xD;
            "lineStyle": {&#xD;
                "color": "white"&#xD;
            }&#xD;
        },&#xD;
        "axisLabel": {&#xD;
            "fontSize": 14&#xD;
        },&#xD;
        "type": "value",&#xD;
        "splitLine": {&#xD;
            "show": true,&#xD;
            "lineStyle": {&#xD;
                "color": "#141D54"&#xD;
            }&#xD;
        }&#xD;
    },&#xD;
    "dataZoom": [dataZoom],&#xD;
    dataset: {&#xD;
        source: []&#xD;
    },&#xD;
    // 添加默认的系列配置，后续会被覆盖&#xD;
    "series": [{&#xD;
        type: 'bar',&#xD;
        name: '总数量',&#xD;
        barWidth: 20,&#xD;
        showBackground: false,&#xD;
        label: {&#xD;
            show: true,&#xD;
            position: 'top',&#xD;
            textStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        },&#xD;
        itemStyle: {&#xD;
            color: {&#xD;
                type: 'linear',&#xD;
                x: 0,&#xD;
                y: 0,&#xD;
                x2: 0,&#xD;
                y2: 1,&#xD;
                colorStops: [{&#xD;
                    offset: 0,&#xD;
                    color: '#00CBD2' // 0% 处的颜色&#xD;
                }, {&#xD;
                    offset: 1,&#xD;
                    color: '#00A2FE' // 100% 处的颜色&#xD;
                }],&#xD;
                global: false&#xD;
            },&#xD;
            borderRadius: [20, 20, 0, 0]&#xD;
        }&#xD;
    }]&#xD;
};&#xD;
result = res;&#xD;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetProblemChartOption"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetProblemChartOption
 * @description   datetime 2024年1月12日16:23:52
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var dataZoom = me.GetChartDataZoom();
var res = {
    "tooltip": {},
    legend: {
        textStyle: {
            color: "#fff",
            fontSize: 15,
            fontWeight: 'bold',
            lineHeight: 20
        }
    },
    "grid": {
        "top": 30,
        "bottom": 50
    },
    "xAxis": {
        type: 'category',
        axisLabel: {
            rotate: 0
        },
        // 设置坐标轴的样式
        axisLine: {
            lineStyle: {
                color: 'white'
            }
        }
    },
    "yAxis": {
        minInterval: 1,
        "show": true,
        "axisLine": {
            "lineStyle": {
                "color": "white"
            }
        },
        "axisLabel": {
            "fontSize": 14
        },
        "type": "value",
        "splitLine": {
            "show": true,
            "lineStyle": {
                "color": "#141D54"
            }
        }
    },
    "dataZoom": [dataZoom],
    dataset: {
        source: []
    },
    "series": [{
        type: 'bar',
        showBackground: false,
        barWidth: me.problemBarWidth,
        backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
        },
        label: {
            show: true,
            position: 'top',
            textStyle: {
                color: 'white'
            }
        },
        itemStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0,
                    color: '#00CBD2' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#00A2FE' // 100% 处的颜色
                }],
                global: false // 缺省为 false
            },
            borderRadius: [20, 20, 0, 0]
        }
    }, {
        type: 'bar',
        barWidth: me.problemBarWidth,
        showBackground: false,
        backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
        },
        label: {
            show: true,
            position: 'top',
            textStyle: {
                color: 'white'
            }
        },
        itemStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0,
                    color: '#99CB75' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#59d208' // 100% 处的颜色
                }],
                global: false // 缺省为 false
            },
            borderRadius: [20, 20, 0, 0]
        }
    }, {
        type: 'bar',
        barWidth: me.problemBarWidth,
        showBackground: false,
        backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
        },
        label: {
            show: true,
            position: 'top',
            textStyle: {
                color: 'white'
            }
        },
        itemStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0,
                    color: '#F3C85D' // 0% 处的颜色
                }, {
                    offset: 1,
                    color: '#fce356' // 100% 处的颜色
                }],
                global: false // 缺省为 false
            },
            borderRadius: [20, 20, 0, 0]
        }
    }]
};

result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetProblemTypeCountSql"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetProblemTypeCountSql
 * @description   获取现场问题处理单的更改工艺文件和更改设计文件的数量sql datetime 2024年1月15日10:27:34
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    childType    
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 * @param    {STRING}    state    
 *
 * @returns    {STRING}
 */
var sql = me.GetProblemTypeDataSql({
    treeId: treeId,
    startDate: startDate,
    state: state,
    endDate: endDate,
    childType: childType
});
result = "select count(*) as count from (" + sql + ")";</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetProblemTypeDataSql"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetProblemTypeDataSql
 * @description   获取现场问题处理单的更改工艺文件和更改设计文件的sql datetime 2024年1月15日14:02:09
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    childType    
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 * @param    {STRING}    state    
 *
 * @returns    {STRING}
 */
var whereSql = me.GetWhereSql({
    treeId: treeId,
    startDate: startDate,
    endDate: endDate,
    fileType: '现场问题处理单'
});
var typeSql = "";
if (childType == "更改设计文件") {
    typeSql = "where (n.ISNEEDCHANGEDESIGNFILE = '1' or n.ISNEEDDEVIATEDESIGNFILE = '1')";
    if (state == "已完成") {
        typeSql += " and n.DESIGN_CHANGE_NO is not null";
    } else if (state == "未完成") {
        typeSql += " and n.DESIGN_CHANGE_NO is null";
    }
} else if (childType == "更改工艺文件") {
    typeSql = "where (n.ISNEEDCHANGETECHFILE = '1' or n.ISNEEDDEVIATETECHFILE = '1')";
    if (state == "已完成") {
        typeSql += " and n.PROCESS_CHANGE_NO is not null";
    } else if (state == "未完成") {
        typeSql += " and n.PROCESS_CHANGE_NO is null";
    }
}

result = "select * " +
    "from (select * from RESULTGATHER " + whereSql + ") m " +
    "         left join XMLDATA_TECHPROBLEM n on n.SOURCE_ID like '%' || m.TABLENAME || '%' and n.RESULT_ID = m.ID " + typeSql;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetQueryCountSql"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetQueryCountSql
 * @description   获取查询数量的sql wanghq 2023年3月30日18:20:32
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 * @param    {STRING}    fileType        {"aspect.defaultValue":"现场问题处理单"}
 *
 * @returns    {STRING}
 */
var isProblemChildType = false;
var type = fileType;
var countSql = "";
if (fileType.indexOf("现场问题处理单|") &gt; -1) {
    var childType = fileType.split("|")[1];
    var childTypeSql = "";
    if (childType == '更改工艺文件' || childType == '更改设计文件') {
        isProblemChildType = true;
        type = childType;
    } else {
        type = "现场问题处理单";
    }
}
if (isProblemChildType) {
    countSql = me.GetProblemTypeCountSql({
        treeId: treeId,
        startDate: startDate,
        endDate: endDate,
        childType: childType
    });
} else {
    var whereSql = me.GetWhereSql({
        treeId: treeId,
        startDate: startDate,
        endDate: endDate,
        fileType: type
    });

    countSql = "select ((select count(*) from PROCESS_CONTROL_RESULT" + whereSql + ") + \
                        (select count(*) from CRAFT_DATA_RESULT" + whereSql + ") + \
                        (select count(*) from DESIGN_DATA_RESULT" + whereSql + ") + \
                        (select count(*) from QUALITY_CONTROL_RESULT" + whereSql + ")) AS COUNT from dual";
}
result = countSql;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetSeverityLevels"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
* @definition    GetSeverityLevels&#xD;
* @description   获取所有严重程度 wanghq 2025年6月4日09:30:40&#xD;
* @implementation    {Script}&#xD;
*&#xD;
*&#xD;
* @returns    {JSON}&#xD;
*/&#xD;
result = { data: ["一级审理", "二级审理", "三级审理"], success: true, msg: "成功" };</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetSituationTypes"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetSituationTypes
 * @description   获取所有情况类型 wanghq 2025年5月23日10:00:00
 * @implementation    {Script}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 查询技术状态更改单的所有情况类型
    var situationTypesSql = "SELECT DISTINCT JTQK FROM BPM_TC_CHANGE_BRANCH WHERE JTQK IS NOT NULL ORDER BY JTQK";
    var situationTypesRs = Things["Thing.DB.Oracle"].RunQuery({ sql: situationTypesSql });

    // 构建情况类型数组
    var situationTypes = [];
    for (var i = 0; i &lt; situationTypesRs.rows.length; i++) {
        situationTypes.push(situationTypesRs.rows[i].JTQK);
    }
    
    res.success = true;
    res.data = situationTypes;
    res.msg = "获取情况类型成功";
} catch (error) {
    res.success = false;
    res.msg = "获取情况类型失败，原因：" + error;
}
result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetSubmitChartOption"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetSubmitChartOption
 * @description   获取产品提交单的柱状图option wanghq 2023年4月17日09:29:19
 * @implementation    {Script}
 *
 * @param    {JSON}    itemCounts    
 * @param    {JSON}    itemNames    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    var dataZoom = me.GetChartDataZoom();
    dataZoom.show = itemNames.array.length &gt; 10;
    var colors = ["#00CBD2", "#00B7E8", "#00A2FE", "#6781E0"];
    var series = [], seriesIndex = 0;

    for (var key in itemCounts) {
        var s = {
            name: key,
            data: itemCounts[key],
            type: 'bar',
            showBackground: false,
            stack: 'total',
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            label: {
                show: true,
                textStyle: {
                    color: 'white'
                }
            },
            // itemStyle: {
            //     color: colors[seriesIndex]
            // },
            barWidth: me.submitBarWidth
        };
        series.push(s);
        seriesIndex++;
    }

    var str = JSON.stringify(itemCounts);
    if (str.indexOf("落焊") &gt; -1) {
        var order = ['不需落焊', '已落焊', '需要但未落焊'];
        series.sort(function (a, b) {
            return order.indexOf(a.name) - order.indexOf(b.name);
        });
    } else if (str.indexOf('交付') &gt; -1) {
        var order = ['已交付', '未交付'];
        series.sort(function (a, b) {
            return order.indexOf(a.name) - order.indexOf(b.name);
        });
    }
    if (seriesIndex == 0) {
        series.push({
            data: [0],
            type: 'bar',
            showBackground: false,
            stack: 'total',
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            label: {
                show: true,
                textStyle: {
                    color: 'white'
                }
            },
            barWidth: 40
        });
    }
    var option = {
        tooltip: {
        },
        legend: {
            textStyle: {
                color: "#fff",
                fontSize: 15,
                fontWeight: 'bold',
                lineHeight: 20
            }
        },
        grid: {
            top: 30,
            bottom: 80
        },
        dataZoom: [dataZoom],
        xAxis: {
            type: 'category',
            data: itemNames.array,
            axisLabel: {
                rotate: 30,
            },
            axisLine: {
                lineStyle: {
                    color: 'white'
                }
            }
        },
        yAxis: {
            show: true,
            minInterval: 1,
            // 设置坐标轴的样式
            axisLine: {
                lineStyle: {
                    color: 'white'
                }
            },
            axisLabel: {
                fontSize: 14
            },
            type: 'value',
            splitLine: {
                show: true,
                lineStyle: {
                    color: "#141D54"
                }
            }
        },
        series: series
    };
    res.success = true;
    res.data = option;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetSubmitListSql"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetSubmitListSql
 * @description   获取查询产品交接单的sql wanghq 2023年4月2日00:11:15
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 *
 * @returns    {STRING}
 */
var fileType = "产品交接单";
var whereSql = me.GetWhereSql({
    treeId: treeId,
    startDate: startDate,
    endDate: endDate,
    fileType: fileType
});
var nullUnitText = '未设置';
var nullText = "未知";
var sql = "select * " +
    "from (select row_number() over (partition by PRODUCTNAME,PRODUCTCODE,BATCHCODE,SUBMITUNIT order by SOURCE_ID desc) groupno, " +
    "             x.* " +
    "      from (select m.ID, " +
    "                   n.PRODUCTNAME, " +
    "                   n.PRODUCTCODE, " +
    "                   n.BATCHCODE, " +
    "                   n.SOURCE_ID, " +
    "                   m.NODECODE, " +
    "                   m.TABLENAME, " +
    "                   m.FILE_FORMAT, " +
    "                   m.FILE_NAME, " +
    "                   m.FILE_TYPE, " +
    "                   m.FILEPATH, " +
    "                   m.CREATE_TIMESTAMP, " +
    "                   nvl(n.ISCERTIFICATE, '" + nullText + "') AS ISCERTIFICATE, " +
    "                   nvl(n.ISLUOHAN, '" + nullText + "')      AS ISLUOHAN, " +
    "                   n.CERTIFICATENUMBER, " +
    "                   n.RESUMENUMBER, " +
    "                   n.OTHER_CERTIFICATE1, " +
    "                   n.OTHER_CERTIFICATE2, " +
    "                   n.ISSUBMIT, " +
    "                   n.LUOHANPHASE, " +
    "                   nvl(n.SUBMITUNIT, '" + nullUnitText + "')   AS SUBMITUNIT " +
    "            from (select ID, " +
    "                         NODECODE, " +
    "                         TABLENAME, " +
    "                         FILEPATH, " +
    "                         FILE_FORMAT, " +
    "                         FILE_NAME, " +
    "                         FILE_TYPE, " +
    "                         CREATE_TIMESTAMP " +
    "                  from RESULTGATHER " + whereSql + ") m " +
    "                     left join V_LATEST_PRODUCT_SUBMIT n " +
    "                               on n.SOURCE_ID like '%' || m.TABLENAME || '%' and n.RESULT_ID = m.ID where n.SUBMITUNIT not like '%812%') x) " +
    "where groupno = 1 order by CREATE_TIMESTAMP desc";
result = sql;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetTempChartOption"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetTempChartOption
 * @description   获取现场临时处理单的柱状图option datetime 2024年11月7日19:27:15
 * @implementation    {Script}
 *
 * @param    {INTEGER}    categorysLen    
 *
 * @returns    {JSON}
 */
var dataZoom = me.GetChartDataZoom();
dataZoom.minValueSpan = 6;
dataZoom.maxValueSpan = 6;
//预设8个颜色
var colors = [
    ['#00CBD2', '#00A2FE'],
    ['#99CB75', '#59d208'],
    ['#F3C85D', '#fce356'],
    ['#8696ef', '#0b2eee'],
    ['#df8fec', '#d108f2'],
    ['#FF0000', '#FFA500'],
    ['#FFA500', '#FFC0CB'],
    ['#FFA500', '#FFC0CB']
]

var series = [];
for (var i = 0; i &lt;= categorysLen; i++) {
    var sObj = {
        type: 'bar',
        showBackground: false,
        barWidth: 15,
        backgroundStyle: {
            color: 'rgba(180, 180, 180, 0.2)'
        },
        label: {
            show: true,
            position: 'top',
            textStyle: {
                color: 'white'
            }
        },
        itemStyle: {
            color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                    offset: 0,
                    color: colors[i][0] // 0% 处的颜色
                }, {
                    offset: 1,
                    color: colors[i][1] // 100% 处的颜色
                }],
                global: false // 缺省为 false
            },
            borderRadius: [20, 20, 0, 0]
        }
    }
    series.push(sObj);
}
var res = {
    "tooltip": {},
    legend: {
        textStyle: {
            color: "#fff",
            fontSize: 15,
            fontWeight: 'bold',
            lineHeight: 20
        }
    },
    "grid": {
        "top": 30,
        "bottom": 50
    },
    "xAxis": {
        type: 'category',
        axisLabel: {
            rotate: 0
        },
        axisLine: {
            lineStyle: {
                color: 'white'
            }
        }
    },
    "yAxis": {
        minInterval: 1,
        "show": true,
        "axisLine": {
            "lineStyle": {
                "color": "white"
            }
        },
        "axisLabel": {
            "fontSize": 14
        },
        "type": "value",
        "splitLine": {
            "show": true,
            "lineStyle": {
                "color": "#141D54"
            }
        }
    },
    "dataZoom": [dataZoom],
    dataset: {
        source: []
    },
    "series": series
};

result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetUserDataTag"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetUserDataTag
 * @description   获取用户的数据标签 wanghq 2023年3月30日09:26:01
 * @implementation    {Script}
 *
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    /**
     * 获取数组的下标
     * @param arr
     * @param val
     * @returns {number}
     */
    function indexOf(arr, val) {
        for (var i = 0; i &lt; arr.length; i++) {
            if (arr[i] == val) {
                return i;
            }
        }
        return -1;
    }
    /**
     * 判断一个元素是否在一个数组中
     * @param arr
     * @param val
     * @returns {boolean}
     */
    function contains(arr, val) {
        return indexOf(arr, val) != -1 ? true : false;
    }


    //查询用户的标签
    var roleSql = "select * from SYS_ROLE where ROLE_ID in(select group_id from SYS_USERGROUP where USER_ID=(select SYS_USER.USER_ID from SYS_USER where USER_NAME='" + username + "'))";
    var roleRs = Things['Thing.DB.Oracle'].RunQuery({ sql: roleSql });
    //查询系统菜单下的数据标签
    var funcSql = "select FUNC_BTN_ID from SYS_FUNC where MENU_ID=(select MENU_ID from SYS_MENU where MENU_NAME='系统菜单')";
    var funcRs = Things['Thing.DB.Oracle'].RunQuery({ sql: funcSql });

    var userAllFuncs = [];
    for (var i = 0; i &lt; roleRs.rows.length; i++) {
        var funcids = roleRs.rows[i]['FUNCIDS'];
        if (funcids) {
            userAllFuncs = userAllFuncs.concat(funcids.split(","));
        }
    }
    var userDataTag = [];
    var sqlUserDataTag = [];
    for (var i = 0; i &lt; funcRs.rows.length; i++) {
        var func_btn_id = funcRs.rows[i]['FUNC_BTN_ID'];
        if (contains(userAllFuncs, func_btn_id)) {
            userDataTag.push(func_btn_id);
            sqlUserDataTag.push("'" + func_btn_id + "'");
        }
    }


    res.success = true;
    res.data = {
        userDataTag: userDataTag,
        sqlUserDataTag: sqlUserDataTag
    };
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetUserRoleModel"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetUserRoleModel
 * @description   获取用户的角色关联的数据标签的型号 wanghq 2023年4月13日10:28:19
 * @implementation    {Script}
 *
 * @param    {STRING}    username    
 * @param    {INTEGER}    isUseScreen        {"aspect.defaultValue":"0"}
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var userDataTag = [];
    var userDataTagRs = me.GetUserDataTag({ username: username });
    if (userDataTagRs.success) {
        userDataTag = userDataTagRs.data.sqlUserDataTag;
    }

    var sql = "select * from PHASE_MODEL where 1=1";
    if (isUseScreen == 1) {
        sql += " and is_use_screen=1";
    }
    if (userDataTag.length &gt; 0) {
        sql += " and UNIQUECODE in (" + userDataTag.join(',') + ")";
    }
    if (treeId != -1) {
        sql += " and treeid=" + treeId;
    }
    var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql }).ToJSON().rows;

    res.success = true;
    res.data = rs;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="GetWhereSql"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    GetWhereSql
 * @description   获取查询的sql wanghq 2023年3月30日18:08:59
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 * @param    {STRING}    fileType        {"aspect.defaultValue":"现场问题处理单"}
 *
 * @returns    {STRING}
 */
var whereSql = " where FILE_TYPE='" + fileType + "'";
if (treeId != -1) {
    whereSql += " and NODECODE in (select ID from DATA_PACKAGE where REFTREEID in( select TREEID from DATAPACKAGETREE s  start with s.TREEID = " + treeId + " CONNECT by  PRIOR s.TREEID = s.PARENTID))";
}

if (startDate != " " &amp;&amp; startDate != "" &amp;&amp; startDate != undefined &amp;&amp; startDate != 'undefined') {
    whereSql += " and CREATE_TIMESTAMP&gt;='" + startDate + " 00:00:00'";
}

if (endDate != " " &amp;&amp; endDate != "" &amp;&amp; startDate != undefined &amp;&amp; startDate != 'undefined') {
    whereSql += " and CREATE_TIMESTAMP&lt;='" + endDate + " 23:59:59'";
}

result = whereSql;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryAllModel"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryAllModel
 * @description   获取所有的型号 wanghq 2023年4月13日10:38:45
 * @implementation    {Script}
 *
 * @param    {STRING}    username    
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 *
 * @returns    {JSON}
 */
result = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryAllModelProcess"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryAllModelProcess
 * @description   查询型号进度 wanghq 2024年5月21日14:21:03
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {

    var queryModelSql = "select * from PHASE_MODEL where CURRENT_AIT_NODE is not null";
    var models = Things['Thing.DB.Oracle'].RunQuery({ sql: queryModelSql }).rows;
    var data = [];
    for (var x = 0; x &lt; models.length; x++) {
        var model = models[x];
        var modelId = model['TREEID'];
        //查询出模板树 整星AIT数据包 下的过程节点
        var tempNodeSql = "select NODENAME " +
            "                 from DATAPACKAGETREE " +
            "                 where PARENTID = (select TREEID " +
            "                                   from DATAPACKAGETREE " +
            "                                   where (nodename like '%整星ait%' or nodename like '%整星AIT%') " +
            "                                   start with TREEID = " + modelId + " " +
            "                                   connect by prior TREEID = PARENTID) " +
            "                 order by NODESORT";
        var tempNodeRs = Things['Thing.DB.Oracle'].RunQuery({ sql: tempNodeSql });
        var tempNodeArr = [];
        for (var i = 0; i &lt; tempNodeRs.rows.length; i++) {
            var tempNodeName = tempNodeRs.rows[i]['NODENAME'];
            tempNodeArr.push(tempNodeName);
        }
        data.push({
            modelName: model['NODENAME'],
            currentNode: model['CURRENT_AIT_NODE'],
            allNode: tempNodeArr.join(",")
        });
    }
    res.success = true;
    res.data = data;
    res.msg = "查询型号进度成功";

} catch (error) {
    res.success = false;
    res.msg = "QueryAllModelProcess-查询型号进度失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryChangeBranchDetail"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryChangeBranchDetail
 * @description   查询技术状态更改单分支详情 wanghq 2025年5月28日21:28:24
 * @implementation    {Script}
 *
 * @param    {STRING}    bindId
 * @param    {STRING}    branchFz     分支编号，如果提供则只查询该分支
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 构建查询SQL
    var branchSql = "SELECT * FROM BPM_TC_CHANGE_BRANCH WHERE BINDID = '" + bindId + "'";
    if (branchFz &amp;&amp; branchFz.trim() !== "") {
        branchSql += " AND FZ = '" + branchFz + "'";
    }
    branchSql += " ORDER BY FZ";
    
    var branchRs = Things['Thing.DB.Oracle'].RunQuery({ sql: branchSql });
    
    // 格式化日期字段
    var branchData = [];
    for (var i = 0; i &lt; branchRs.rows.length; i++) {
        var branch = extractThingworxData(branchRs.rows[i]);
        
        if (branch.CREATE_TIME) {
            try {
                branch.CREATE_TIME_STR = dateFormat(new Date(branch.CREATE_TIME), "yyyy-MM-dd HH:mm:ss");
            } catch (e) {
                branch.CREATE_TIME_STR = branch.CREATE_TIME.toString();
            }
        }
        if (branch.UPDATE_TIME) {
            try {
                branch.UPDATE_TIME_STR = dateFormat(new Date(branch.UPDATE_TIME), "yyyy-MM-dd HH:mm:ss");
            } catch (e) {
                branch.UPDATE_TIME_STR = branch.UPDATE_TIME.toString();
            }
        }
        branchData.push(branch);
    }
    
    // 获取字段注释信息
    var commentSql = "SELECT COLUMN_NAME, COMMENTS FROM USER_COL_COMMENTS WHERE TABLE_NAME = 'BPM_TC_CHANGE_BRANCH'";
    var commentRs = Things['Thing.DB.Oracle'].RunQuery({ sql: commentSql });
    
    var comments = {};
    for (var i = 0; i &lt; commentRs.rows.length; i++) {
        comments[commentRs.rows[i].COLUMN_NAME] = commentRs.rows[i].COMMENTS;
    }
    
    res.code = 0;
    res.success = true;
    res.msg = "查询成功";
    res.data = {
        branchData: branchData,
        comments: comments
    };
} catch (error) {
    res.code = -1;
    res.success = false;
    res.msg = "查询失败，原因：" + error;
    res.data = null;
}

/**
 * 提取ThingWorx格式的数据
 * @param {Object} row 数据库查询结果行
 * @returns {Object} 提取后的数据对象
 */
function extractThingworxData(row) {
    var result = {};
    
    for (var key in row) {
        if (row.hasOwnProperty(key)) {
            var value = row[key];
            
            // 处理ThingWorx对象格式（包含value属性的对象）
            if (value !== null &amp;&amp; typeof value === 'object' &amp;&amp; value.hasOwnProperty('value')) {
                // 根据baseType处理不同类型的值
                if (value.baseType === 'STRING' || value.baseType === 'NUMBER' || value.baseType === 'INTEGER') {
                    result[key] = value.value;
                } else if (value.baseType === 'DATETIME') {
                    // 处理日期时间类型
                    try {
                        var dateObj = new Date(value.value);
                        result[key] = dateObj;
                    } catch (e) {
                        result[key] = value.value;
                    }
                } else if (value.baseType === 'BOOLEAN') {
                    // 处理布尔类型
                    result[key] = value.value === true || value.value === 'true' || value.value === 1 || value.value === '1';
                } else {
                    // 其他类型，使用value属性
                    result[key] = value.value;
                }
            } else {
                // 非ThingWorx对象格式，直接使用值
                result[key] = value;
            }
        }
    }
    
    return result;
}

result = res; </code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryChangeOrderCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    QueryChangeOrderCount&#xD;
 * @description   查询技术状态更改单的数量 wanghq 2025年5月22日10:45:00&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}&#xD;
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}&#xD;
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}&#xD;
 * @param    {STRING}    username        {"aspect.defaultValue":" "}&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
var res = {};&#xD;
try {&#xD;
    // 定义状态数据对象，用于存储各型号的完成/未完成状态数据&#xD;
    var statusData = {&#xD;
        finished: {},&#xD;
        unfinished: {},&#xD;
        branchFinished: {},&#xD;
        branchUnfinished: {},&#xD;
        situationCounts: {},&#xD;
        situationStatus: {},&#xD;
        modelMap: {}  // 添加modelMap用于存储型号名称到ID的映射&#xD;
    };&#xD;
&#xD;
    // 查询所有情况类型&#xD;
    var situationTypesSql = "SELECT DISTINCT JTQK FROM BPM_TC_CHANGE_BRANCH WHERE JTQK IS NOT NULL ORDER BY JTQK";&#xD;
    var situationTypesRs = Things["Thing.DB.Oracle"].RunQuery({ sql: situationTypesSql });&#xD;
&#xD;
    // 构建情况类型数组&#xD;
    var situationTypes = [];&#xD;
    for (var i = 0; i &lt; situationTypesRs.rows.length; i++) {&#xD;
        situationTypes.push(situationTypesRs.rows[i].JTQK);&#xD;
    }&#xD;
&#xD;
    // 初始化情况类型计数对象和状态对象&#xD;
    for (var i = 0; i &lt; situationTypes.length; i++) {&#xD;
        var situationType = situationTypes[i];&#xD;
        statusData.situationCounts[situationType] = {};&#xD;
&#xD;
        // 初始化情况状态对象&#xD;
        statusData.situationStatus[situationType] = {&#xD;
            finished: {},&#xD;
            unfinished: {}&#xD;
        };&#xD;
    }&#xD;
&#xD;
    var datas = [];&#xD;
&#xD;
    // 查询型号列表&#xD;
    var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });&#xD;
&#xD;
    // 构建型号ID列表&#xD;
    var modelArr = [];&#xD;
    for (var i = 0; i &lt; modelRs.data.length; i++) {&#xD;
        modelArr.push(modelRs.data[i].TREEID);&#xD;
    }&#xD;
&#xD;
    if (modelArr.length &gt; 0) {&#xD;
        // 将型号ID列表转为逗号分隔的字符串&#xD;
        var models = modelArr.join(",");&#xD;
&#xD;
        // 查询统计数据&#xD;
        var countSql = "SELECT n.NODENAME, m.MODEL_ID, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单' THEN m.STAT_COUNT END) AS order_count, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单_分支' THEN m.STAT_COUNT END) AS branch_count, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单~已完成' THEN m.STAT_COUNT END) AS finished_count, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单~未完成' THEN m.STAT_COUNT END) AS unfinished_count, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单_分支~已完成' THEN m.STAT_COUNT END) AS branch_finished_count, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单_分支~未完成' THEN m.STAT_COUNT END) AS branch_unfinished_count";&#xD;
&#xD;
        // 添加各种情况的统计&#xD;
        for (var i = 0; i &lt; situationTypes.length; i++) {&#xD;
            var situationType = situationTypes[i];&#xD;
            var safeFieldName = "SIT_" + i;&#xD;
&#xD;
            countSql += ", SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单_" + situationType + "' THEN m.STAT_COUNT END) AS " +&#xD;
                safeFieldName + "_count";&#xD;
&#xD;
            countSql += ", SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单_" + situationType + "~已完成' THEN m.STAT_COUNT END) AS " +&#xD;
                safeFieldName + "_finished_count";&#xD;
&#xD;
            countSql += ", SUM(CASE WHEN m.STAT_TYPE = '技术状态更改单_" + situationType + "~未完成' THEN m.STAT_COUNT END) AS " +&#xD;
                safeFieldName + "_unfinished_count";&#xD;
        }&#xD;
&#xD;
        countSql += " FROM MODEL_STATISTICS m " +&#xD;
            "         LEFT JOIN PHASE_MODEL n ON m.MODEL_ID = n.TREEID " +&#xD;
            "WHERE m.MODEL_ID IN (" + models + ") " +&#xD;
            "GROUP BY n.NODENAME, m.MODEL_ID";&#xD;
&#xD;
        var statRs = Things['Thing.DB.Oracle'].RunQuery({ sql: countSql });&#xD;
&#xD;
        for (var i = 0; i &lt; statRs.rows.length; i++) {&#xD;
            var stat = statRs.rows[i];&#xD;
            var modelId = stat.MODEL_ID;&#xD;
            var modelName = stat.NODENAME;&#xD;
            var orderCount = stat.ORDER_COUNT || 0;&#xD;
            var branchCount = stat.BRANCH_COUNT || 0;&#xD;
            var finishedCount = stat.FINISHED_COUNT || 0;&#xD;
            var unfinishedCount = stat.UNFINISHED_COUNT || 0;&#xD;
            var branchFinishedCount = stat.BRANCH_FINISHED_COUNT || 0;&#xD;
            var branchUnfinishedCount = stat.BRANCH_UNFINISHED_COUNT || 0;&#xD;
&#xD;
            statusData.finished[modelId] = finishedCount;&#xD;
            statusData.unfinished[modelId] = unfinishedCount;&#xD;
            statusData.branchFinished[modelId] = branchFinishedCount;&#xD;
            statusData.branchUnfinished[modelId] = branchUnfinishedCount;&#xD;
            statusData.modelMap[modelName] = modelId;&#xD;
&#xD;
            for (var j = 0; j &lt; situationTypes.length; j++) {&#xD;
                var situationType = situationTypes[j];&#xD;
                var safeFieldName = "SIT_" + j;&#xD;
                var countField = (safeFieldName + "_COUNT").toUpperCase();&#xD;
                var situationCount = stat[countField] || 0;&#xD;
                statusData.situationCounts[situationType][modelId] = situationCount;&#xD;
&#xD;
                var finishedField = (safeFieldName + "_FINISHED_COUNT").toUpperCase();&#xD;
                var situationFinishedCount = stat[finishedField] || 0;&#xD;
                statusData.situationStatus[situationType].finished[modelId] = situationFinishedCount;&#xD;
&#xD;
                var unfinishedField = (safeFieldName + "_UNFINISHED_COUNT").toUpperCase();&#xD;
                var situationUnfinishedCount = stat[unfinishedField] || 0;&#xD;
                statusData.situationStatus[situationType].unfinished[modelId] = situationUnfinishedCount;&#xD;
            }&#xD;
&#xD;
            if (orderCount &gt; 0 || branchCount &gt; 0) {&#xD;
                var obj = {&#xD;
                    name: modelName + "~~~" + modelId,&#xD;
                    value: [orderCount, branchCount]&#xD;
                };&#xD;
&#xD;
                for (var j = 0; j &lt; situationTypes.length; j++) {&#xD;
                    var countField = ("SIT_" + j + "_COUNT").toUpperCase();&#xD;
                    var situationCount = stat[countField] || 0;&#xD;
                    obj.value.push(situationCount);&#xD;
                }&#xD;
&#xD;
                datas.push(obj);&#xD;
            }&#xD;
        }&#xD;
    }&#xD;
&#xD;
    var gradientColors = [&#xD;
        { start: '#A78BFA', end: '#7C3AED' },&#xD;
        { start: '#FDBA74', end: '#EA580C' },&#xD;
        { start: '#F9A8D4', end: '#DB2777' },&#xD;
        { start: '#FCA5A5', end: '#DC2626' },&#xD;
        { start: '#F3C85D', end: '#fce356' },&#xD;
        { start: '#5EEAD4', end: '#0D9488' },&#xD;
        { start: '#93C5FD', end: '#2563EB' },&#xD;
        { start: '#D6B088', end: '#92400E' }&#xD;
    ];&#xD;
&#xD;
    var option = me.GetChangeOrderChartOption();&#xD;
    var dataset = [['名称', '总数量', '分支总数量']];&#xD;
&#xD;
    for (var i = 0; i &lt; situationTypes.length; i++) {&#xD;
        dataset[0].push(situationTypes[i]);&#xD;
    }&#xD;
&#xD;
    // 按照总数量从大到小排序&#xD;
    datas.sort(function (a, b) {&#xD;
        return b.value[0] - a.value[0];&#xD;
    });&#xD;
&#xD;
    for (var i = 0; i &lt; datas.length; i++) {&#xD;
        var row = [datas[i].name];&#xD;
        for (var j = 0; j &lt; datas[i].value.length; j++) {&#xD;
            row.push(datas[i].value[j]);&#xD;
        }&#xD;
        dataset.push(row);&#xD;
    }&#xD;
&#xD;
    var series = [];&#xD;
&#xD;
    series.push({&#xD;
        name: '总数量',&#xD;
        type: 'bar',&#xD;
        barWidth: 20,&#xD;
        showBackground: false,&#xD;
        label: {&#xD;
            show: true,&#xD;
            position: 'top',&#xD;
            textStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        },&#xD;
        itemStyle: {&#xD;
            color: {&#xD;
                type: 'linear',&#xD;
                x: 0, y: 0, x2: 0, y2: 1,&#xD;
                colorStops: [{&#xD;
                    offset: 0,&#xD;
                    color: '#00CBD2'&#xD;
                }, {&#xD;
                    offset: 1,&#xD;
                    color: '#00A2FE'&#xD;
                }],&#xD;
                global: false&#xD;
            },&#xD;
            borderRadius: [20, 20, 0, 0]&#xD;
        }&#xD;
    });&#xD;
&#xD;
    // 添加分支总数量系列&#xD;
    series.push({&#xD;
        name: '分支总数量',&#xD;
        type: 'bar',&#xD;
        barWidth: 20,&#xD;
        showBackground: false,&#xD;
        label: {&#xD;
            show: true,&#xD;
            position: 'top',&#xD;
            textStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        },&#xD;
        itemStyle: {&#xD;
            color: {&#xD;
                type: 'linear',&#xD;
                x: 0, y: 0, x2: 0, y2: 1,&#xD;
                colorStops: [{&#xD;
                    offset: 0,&#xD;
                    color: '#99CB75'&#xD;
                }, {&#xD;
                    offset: 1,&#xD;
                    color: '#59d208'&#xD;
                }],&#xD;
                global: false&#xD;
            },&#xD;
            borderRadius: [20, 20, 0, 0]&#xD;
        }&#xD;
    });&#xD;
&#xD;
    // 添加各种情况的数据系列&#xD;
    for (var i = 0; i &lt; situationTypes.length; i++) {&#xD;
        // 选择颜色，使用取模运算确保不会超出颜色数组范围&#xD;
        var colorIndex = i % gradientColors.length;&#xD;
        var gradientColor = gradientColors[colorIndex];&#xD;
&#xD;
        series.push({&#xD;
            name: situationTypes[i],&#xD;
            type: 'bar',&#xD;
            barWidth: 20,&#xD;
            showBackground: false,&#xD;
            label: {&#xD;
                show: true,&#xD;
                position: 'top',&#xD;
                textStyle: {&#xD;
                    color: 'white'&#xD;
                }&#xD;
            },&#xD;
            itemStyle: {&#xD;
                color: {&#xD;
                    type: 'linear',&#xD;
                    x: 0, y: 0, x2: 0, y2: 1,&#xD;
                    colorStops: [{&#xD;
                        offset: 0,&#xD;
                        color: gradientColor.start&#xD;
                    }, {&#xD;
                        offset: 1,&#xD;
                        color: gradientColor.end&#xD;
                    }],&#xD;
                    global: false&#xD;
                },&#xD;
                borderRadius: [20, 20, 0, 0]&#xD;
            }&#xD;
        });&#xD;
    }&#xD;
&#xD;
    option.series = series;&#xD;
&#xD;
    // 设置数据集&#xD;
    option.dataset = {&#xD;
        source: dataset&#xD;
    };&#xD;
&#xD;
    // 记录颜色分配情况&#xD;
    for (var i = 0; i &lt; situationTypes.length; i++) {&#xD;
        var colorIndex = i % gradientColors.length;&#xD;
    }&#xD;
&#xD;
    // 验证数据完整性&#xD;
    if (datas.length === 0) {&#xD;
    }&#xD;
&#xD;
    if (series.length !== situationTypes.length + 2) {&#xD;
    }&#xD;
&#xD;
    // 计算各系列的总和&#xD;
    var totalObj = {};&#xD;
    // 初始化各系列总和&#xD;
    for (var i = 1; i &lt; dataset[0].length; i++) {&#xD;
        totalObj[dataset[0][i]] = 0;&#xD;
    }&#xD;
    // 累加各型号的数据&#xD;
    for (var i = 1; i &lt; dataset.length; i++) {&#xD;
        for (var j = 1; j &lt; dataset[i].length; j++) {&#xD;
            totalObj[dataset[0][j]] += dataset[i][j];&#xD;
        }&#xD;
    }&#xD;
&#xD;
    res.success = true;&#xD;
    res.data = {&#xD;
        option: option,&#xD;
        statusData: statusData,&#xD;
        total: totalObj&#xD;
    };&#xD;
    res.msg = "查询技术状态更改单统计数据成功";&#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    res.msg = "查询技术状态更改单统计数据失败，原因：" + error;&#xD;
}&#xD;
result = res;&#xD;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryChangeOrderList"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    QueryChangeOrderList&#xD;
 * @description   查询技术状态更改单列表 wanghq 2025年5月22日11:15:00&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}&#xD;
 * @param    {STRING}    username        {"aspect.defaultValue":" "}&#xD;
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}&#xD;
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}&#xD;
 * @param    {STRING}    status        {"aspect.defaultValue":"all"}&#xD;
 * @param    {STRING}    situation        {"aspect.defaultValue":"all"}&#xD;
 * @param    {STRING}    queryType        {"aspect.defaultValue":"all"}&#xD;
 * @param    {NUMBER}    page        {"aspect.defaultValue":"1"}&#xD;
 * @param    {NUMBER}    limit        {"aspect.defaultValue":"10"}&#xD;
 * @param    {BOOLEAN}    isAllData        {"aspect.defaultValue":"false"}&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
var res = {};&#xD;
try {&#xD;
    // 计算分页参数&#xD;
    var startRowno = (page - 1) * limit + 1;&#xD;
    var endRowno = page * limit;&#xD;
    &#xD;
    // 获取型号名称&#xD;
    var modelName = "";&#xD;
    var phaseCode = "";&#xD;
    if (treeId != -1) {&#xD;
        var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });&#xD;
        if (modelRs.data.length &gt; 0) {&#xD;
            modelName = modelRs.data[0].MODEL_NAME;&#xD;
            phaseCode = modelRs.data[0].PHASE_CODE;&#xD;
        }&#xD;
    }&#xD;
    &#xD;
    // 根据查询类型构建不同的SQL&#xD;
    var baseSql = "";&#xD;
    &#xD;
    if (queryType === "main") {&#xD;
        // 仅查询主表 - 总数量查询&#xD;
        baseSql = "SELECT o.*, " +&#xD;
            "CASE WHEN o.ISEND = 1 THEN '已完成' ELSE '未完成' END as STATUS, " +&#xD;
            "NULL as FZ, NULL as JTQK, " +&#xD;
            "TO_CHAR(o.CREATE_TIME, 'YYYY-MM-DD') as CREATE_DATE " +&#xD;
            "FROM BPM_TC_CHANGE_ORDER o " +&#xD;
            "WHERE 1=1";&#xD;
    } else if (queryType === "branch") {&#xD;
        // 分支表模式：查询主表关联分支表，明确指定所有字段避免Oracle列名冲突&#xD;
        baseSql = "SELECT " +&#xD;
            // 主表字段&#xD;
            "o.BILLNO, o.ISEND, o.VBILLSTATUS, o.ZDRQ, o.ZDR, o.ZDRID, o.XH, o.YZJD, " +&#xD;
            "o.CPDH, o.PCH_SL, o.GGDH, o.ZLGZQRRY, o.ZLGZQRRYID, o.GGDFFBBH, o.GGDFFBMC, " +&#xD;
            "o.PDMFILELINK, o.LZDW, o.SJSMC, o.SRWJ_GGQ, o.SRWJ_GGQ_MC, o.SRWJ_GGQ_BB, " +&#xD;
            "o.SRWJ_GGH, o.SRWJ_GGH_MC, o.SRWJ_GGH_BB, o.GGXM, o.QSR, o.QSRQ, " +&#xD;
            "o.BINDID, o.CREATE_TIME, o.UPDATE_TIME, " +&#xD;
            "CASE WHEN o.ISEND = 1 THEN '已完成' ELSE '未完成' END as STATUS, " +&#xD;
            "TO_CHAR(o.CREATE_TIME, 'YYYY-MM-DD') as CREATE_DATE, " +&#xD;
            // 分支表字段&#xD;
            "b.FZ, b.YWBM, b.YWBMID, b.ISEND as B_ISEND, b.GYRY, b.GYSID, b.JYSID, b.JTQK, " +&#xD;
            "b.SCWJ_GGQ, b.SCWJ_GGQ_MC, b.SCWJ_GGQ_BB, b.SCWJ_GGH, b.SCWJ_GGH_MC, b.SCWJ_GGH_BB, " +&#xD;
            "b.QTSM1, b.GYS, b.RQ1, b.ZFCP_ZX_GGSL, b.ZFCP_ZX_GGJL, b.ZFCP_KF_GGSL, b.ZFCP_KF_GGJL, " +&#xD;
            "b.ZFCP_YJ_GGSL, b.ZFCP_YJ_GGJL, b.YQJ_GGQ, b.YQJ_GGH, b.QTSM2, b.JYY, b.RQ2, " +&#xD;
            "b.YYBMQR, b.QZ3, b.RQ3, b.ZLSQR, b.QZ4, b.RQ4, b.YJRY_GY, b.YJRYID_GY, " +&#xD;
            "b.YJRY_JY, b.YJRYID_JY, b.BINDID as B_BINDID, b.CREATE_TIME as B_CREATE_TIME, b.UPDATE_TIME as B_UPDATE_TIME " +&#xD;
            "FROM BPM_TC_CHANGE_ORDER o " +&#xD;
            "INNER JOIN BPM_TC_CHANGE_BRANCH b ON o.BINDID = b.BINDID " +&#xD;
            "WHERE 1=1";&#xD;
    } else {&#xD;
        // 默认模式：保持向后兼容，明确指定所有字段避免Oracle列名冲突&#xD;
        baseSql = "SELECT " +&#xD;
            // 主表字段&#xD;
            "o.BILLNO, o.ISEND, o.VBILLSTATUS, o.ZDRQ, o.ZDR, o.ZDRID, o.XH, o.YZJD, " +&#xD;
            "o.CPDH, o.PCH_SL, o.GGDH, o.ZLGZQRRY, o.ZLGZQRRYID, o.GGDFFBBH, o.GGDFFBMC, " +&#xD;
            "o.PDMFILELINK, o.LZDW, o.SJSMC, o.SRWJ_GGQ, o.SRWJ_GGQ_MC, o.SRWJ_GGQ_BB, " +&#xD;
            "o.SRWJ_GGH, o.SRWJ_GGH_MC, o.SRWJ_GGH_BB, o.GGXM, o.QSR, o.QSRQ, " +&#xD;
            "o.BINDID, o.CREATE_TIME, o.UPDATE_TIME, " +&#xD;
            "CASE WHEN o.ISEND = 1 THEN '已完成' ELSE '未完成' END as STATUS, " +&#xD;
            "TO_CHAR(o.CREATE_TIME, 'YYYY-MM-DD') as CREATE_DATE, " +&#xD;
            // 分支表字段（使用LEFT JOIN，可能为NULL）&#xD;
            "b.FZ, b.YWBM, b.YWBMID, b.ISEND as B_ISEND, b.GYRY, b.GYSID, b.JYSID, b.JTQK, " +&#xD;
            "b.SCWJ_GGQ, b.SCWJ_GGQ_MC, b.SCWJ_GGQ_BB, b.SCWJ_GGH, b.SCWJ_GGH_MC, b.SCWJ_GGH_BB, " +&#xD;
            "b.QTSM1, b.GYS, b.RQ1, b.ZFCP_ZX_GGSL, b.ZFCP_ZX_GGJL, b.ZFCP_KF_GGSL, b.ZFCP_KF_GGJL, " +&#xD;
            "b.ZFCP_YJ_GGSL, b.ZFCP_YJ_GGJL, b.YQJ_GGQ, b.YQJ_GGH, b.QTSM2, b.JYY, b.RQ2, " +&#xD;
            "b.YYBMQR, b.QZ3, b.RQ3, b.ZLSQR, b.QZ4, b.RQ4, b.YJRY_GY, b.YJRYID_GY, " +&#xD;
            "b.YJRY_JY, b.YJRYID_JY, b.BINDID as B_BINDID, b.CREATE_TIME as B_CREATE_TIME, b.UPDATE_TIME as B_UPDATE_TIME " +&#xD;
            "FROM BPM_TC_CHANGE_ORDER o " +&#xD;
            "LEFT JOIN BPM_TC_CHANGE_BRANCH b ON o.BINDID = b.BINDID " +&#xD;
            "WHERE 1=1";&#xD;
    }&#xD;
    &#xD;
    // 添加型号筛选条件&#xD;
    if (modelName) {&#xD;
        baseSql += " AND o.XH = '" + modelName + "'";&#xD;
    }&#xD;
    if (phaseCode) {&#xD;
        baseSql += " AND o.YZJD = '" + phaseCode + "'";&#xD;
    }&#xD;
    &#xD;
&#xD;
    // 添加日期筛选条件&#xD;
    if (startDate &amp;&amp; startDate.trim() !== "") {&#xD;
        baseSql += " AND o.CREATE_TIME &gt;= TO_DATE('" + startDate + "', 'YYYY-MM-DD')";&#xD;
    }&#xD;
    if (endDate &amp;&amp; endDate.trim() !== "") {&#xD;
        baseSql += " AND o.CREATE_TIME &lt;= TO_DATE('" + endDate + " 23:59:59', 'YYYY-MM-DD HH24:MI:SS')";&#xD;
    }&#xD;
    &#xD;
    // 添加状态筛选条件&#xD;
    if (status &amp;&amp; status !== "all") {&#xD;
        if (status === "finished") {&#xD;
            baseSql += " AND o.ISEND = 1"; // 1表示已完成&#xD;
        } else {&#xD;
            baseSql += " AND o.ISEND = 0"; // 0表示未完成&#xD;
        }&#xD;
    }&#xD;
    &#xD;
    // 添加情况筛选条件 - 仅在查询分支表时有效&#xD;
    if (situation &amp;&amp; situation !== "all" &amp;&amp; (queryType === "branch" || queryType === "all")) {&#xD;
        baseSql += " AND b.JTQK = '" + situation + "'";&#xD;
    }&#xD;
    &#xD;
    // 构建查询SQL&#xD;
    var querySql = "";&#xD;
    var countSql = "";&#xD;
    &#xD;
    if (isAllData) {&#xD;
        // 不分页，直接查询所有数据&#xD;
        if (queryType === "main") {&#xD;
            querySql = baseSql + " ORDER BY o.CREATE_TIME DESC, o.BILLNO";&#xD;
        } else {&#xD;
            querySql = baseSql + " ORDER BY o.CREATE_TIME DESC, o.BILLNO, b.FZ";&#xD;
        }&#xD;
    } else {&#xD;
        // 分页查询&#xD;
        var orderBy = queryType === "main" ? &#xD;
            " ORDER BY o.CREATE_TIME DESC, o.BILLNO" :&#xD;
            " ORDER BY o.CREATE_TIME DESC, o.BILLNO, b.FZ";&#xD;
        &#xD;
        querySql = "SELECT * FROM (SELECT t.*, ROWNUM as rn FROM (" + &#xD;
            baseSql + orderBy + &#xD;
            ") t WHERE ROWNUM &lt;= " + endRowno + ") WHERE rn &gt;= " + startRowno;&#xD;
    }&#xD;
    &#xD;
    // 计数SQL&#xD;
    countSql = "SELECT COUNT(*) as COUNT FROM (" + baseSql + ")";&#xD;
    &#xD;
    // 执行查询&#xD;
    var dataRs = Things['Thing.DB.Oracle'].RunQuery({ sql: querySql }).ToJSON().rows;&#xD;
    var countRs = Things['Thing.DB.Oracle'].RunQuery({ sql: countSql });&#xD;
    &#xD;
    // 处理结果&#xD;
    var total = countRs.rows[0].COUNT;&#xD;
    &#xD;
    res.code = 0;  // 0表示成功，layui表格格式&#xD;
    res.data = dataRs;&#xD;
    res.count = total;  // layui表格使用count字段表示总数&#xD;
    res.msg = "查询技术状态更改单列表成功";&#xD;
} catch (error) {&#xD;
    res.code = -1;  // 非0表示失败，layui表格格式&#xD;
    res.data = [];&#xD;
    res.count = 0;&#xD;
    res.msg = "查询技术状态更改单列表失败，原因：" + error;&#xD;
}&#xD;
result = res;&#xD;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryFanyi"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryFanyi
 * @description   范燚指定的统计 wanghq 2024年10月24日14:16:12
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var resData = [];
    var names = ['结构装配总结', '低频电缆网总结确认表'];
    //查询型号下A表的数量
    for (var i = 0; i &lt; names.length; i++) {
        var obj = {};
        var namei = names[i];
        obj.name = namei;
        var aRes = Things['Thing.DB.Oracle'].RunQuery({
            sql: "select MODEL_NAME, count(*) as a_count from REPORT_TREE where name like '%" + namei + "%' group by name, MODEL_NAME"
        });
        obj.modelCount = aRes.rows.length;
        var models = [];
        for (var j = 0; j &lt; aRes.rows.length; j++) {
            var row = aRes.rows[j];
            //查询型号下的B表数量
            var bRes = Things['Thing.DB.Oracle'].RunQuery({
                sql: "select count(*) as b_count from QUALITY_REPORT where PID in (" +
                    "select ID from REPORT_TREE where MODEL_NAME = '" + row["MODEL_NAME"] + "' and like '%" + namei + "%')"
            });

            models.push({
                modelName: row["MODEL_NAME"],
                modelACount: row["A_COUNT"],
                modelBCount: bRes.rows[0]["B_COUNT"]
            });
        }
        obj.models = models;
        resData.push(obj);
    }
    //全景影像模糊查询
    var qRes = Things['Thing.DB.Oracle'].RunQuery({
        sql: "select  MODEL_NAME, count(*) as count from REPORT_TREE where name like '%全景影像%' group by MODEL_NAME"
    });
    if (qRes.rows.length &gt; 0) {
        var qObj = {
            name: "全景影像模糊查询",
            modelCount: qRes.rows.length,
            models: []
        };
        for (var i = 0; i &lt; qRes.rows.length; i++) {
            var row = qRes.rows[i];
            qObj.models.push({
                modelName: row["MODEL_NAME"],
                modelCount: row["COUNT"],
            });
        }
        resData.push(qObj);
    }
    //型号总数统计
    var modelCount = Things['Thing.DB.Oracle'].RunQuery({
        sql: "select count(distinct model_name) as count from REPORT_TREE where MODEL_NAME is not null"
    }).rows[0]["COUNT"];

    resData.push({
        name: "型号总数",
        count: modelCount
    });

    //电测试数据型号数量
    var testModelCount = Things['Thing.DB.Oracle'].RunQuery({
        sql: "select count(distinct b.MODEL_NAME) count from QUALITY_REPORT a left join REPORT_TREE b on to_char(a.ID) = b.ID " +
            "where a.IS_ELECTRIC_TEST =1"
    }).rows[0]["COUNT"];

    resData.push({
        name: "电测试型号总数",
        count: testModelCount
    });
    res.success = true;
    res.data = resData;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryList"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryList
 * @description   查询清单列表 wanghq 2023年3月30日23:04:04
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 * @param    {STRING}    fileType        {"aspect.defaultValue":"现场问题处理单"}
 * @param    {NUMBER}    page        {"aspect.defaultValue":"1.0"}
 * @param    {NUMBER}    limit        {"aspect.defaultValue":"10.0"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    if (fileType.indexOf('现场临时处理单') &gt; -1) {
        // 解析fileType参数
        var parts = fileType.split("|");
        var childType = parts[1] || '';
        var status = '';

        // 获取完成状态
        if (fileType.indexOf('|已完成') &gt; -1) {
            status = "已完成";
        } else if (fileType.indexOf('|未完成') &gt; -1) {
            status = "未完成";
        }

        // 在调用QueryTempList时传递status参数
        res = me.QueryTempList({
            page: page,
            limit: limit,
            treeId: treeId,
            startDate: startDate,
            endDate: endDate,
            childType: childType,
            status: status
        });
    } else {
        var isProblemChildType = false;
        var type = fileType;
        var countSql = "";
        var querySql = "";
        var childTypeSql = "";
        var statusFilter = "";
        var fileTypeParts = fileType.split("|");
        
        // 判断是否是现场问题处理单，并解析其状态
        if (fileType.indexOf("现场问题处理单") &gt; -1) {
            var childType = "";
            var status = "";
            
            if (fileTypeParts.length &gt;= 2) {
                childType = fileTypeParts[1];
                
                // 如果有第三个部分，则为状态
                if (fileTypeParts.length &gt;= 3) {
                    status = fileTypeParts[2];
                }
            }
            
            if (childType === '全部') {
                // 全部类型
                type = "现场问题处理单";
                
                // 闭环状态筛选 - 使用STATE字段
                if (status === '已闭环') {
                    statusFilter = " AND n.STATE = '7'";
                } else if (status === '未闭环') {
                    statusFilter = " AND (n.STATE != '7' OR n.STATE IS NULL)";
                }
            } else if (childType == '更改工艺文件' || childType == '更改设计文件') {
                isProblemChildType = true;
                type = childType;
                
                // 这里不再处理状态筛选，后面会使用me.GetProblemTypeDataSql方法
                // 该方法会处理状态参数
            } else {
                type = "现场问题处理单";
            }
        }

        // 判断是否需要分页
        var isAllData = limit === -1 || limit === 0;
        var startRowno = isAllData ? 1 : (page - 1) * limit + 1;
        var endRowno = isAllData ? 999999 : page * limit;

        // 根据文件类型构建不同的查询
        if (isProblemChildType) {
            // 获取状态参数
            var state = "";
            if (fileTypeParts.length &gt;= 3) {
                state = fileTypeParts[2];
            }
            
            // 使用参考 UpdateProblemCount.js 中使用的查询方式
            var sql = me.GetProblemTypeDataSql({
                treeId: treeId,
                startDate: startDate,
                endDate: endDate,
                childType: type,
                state: state  // 传递状态参数
            });

            if (isAllData) {
                // 不分页，直接查询所有数据
                querySql = sql + " order by CREATE_TIMESTAMP desc";
            } else {
                // 分页查询
                querySql = "select * from (select s.* ,ROWNUM rowno from " +
                    "(" + sql + " order by CREATE_TIMESTAMP desc) s ) where  rowno &gt;= " + startRowno + " and  rowno &lt;=" + endRowno;
            }
            
            // 更新计数SQL，确保使用相同的筛选条件
            countSql = "select count(*) as COUNT from (" + sql + ")";
            
        } else if (fileType.indexOf("现场问题处理单|全部") &gt; -1) {
            // 为"现场问题处理单|全部"添加状态字段
            var whereSql = me.GetWhereSql({
                treeId: treeId,
                startDate: startDate,
                endDate: endDate,
                fileType: type
            });
            
            // 构建查询，确保包含STATE字段
            var baseSql = "select m.ID, m.TABLENAME, m.FILE_NUMBER, m.FILE_TYPE, m.FILE_NAME, m.FILEPATH, " +
                "m.FILE_FORMAT, m.NODECODE, m.GATHERING_METHOD, m.SOURCE_SYSTEM, m.CREATE_TIMESTAMP, " +
                "n.STATE " + // 添加STATE字段用于闭环状态判断
                "from (select * from RESULTGATHER" + whereSql + ") m " +
                "left join XMLDATA_TECHPROBLEM n on n.SOURCE_ID like '%' || m.TABLENAME || '%' and n.RESULT_ID = m.ID " +
                "where 1=1";
                
            // 添加闭环状态筛选条件
            if (fileType.indexOf("|已闭环") &gt; -1) {
                baseSql += " AND n.STATE = '7'";
            } else if (fileType.indexOf("|未闭环") &gt; -1) {
                baseSql += " AND (n.STATE != '7' OR n.STATE IS NULL)";
            }
                
            if (isAllData) {
                // 不分页，直接查询所有数据
                querySql = baseSql + " order by m.CREATE_TIMESTAMP desc";
            } else {
                // 分页查询
                querySql = "select * from (select s.* ,ROWNUM rowno from " +
                    "(" + baseSql + " order by m.CREATE_TIMESTAMP desc) s ) where rowno &gt;= " + startRowno + " and rowno &lt;=" + endRowno;
            }
            
            // 更新计数SQL
            countSql = "select count(*) as COUNT from (" + baseSql + ")";
            
        } else {
            // 其他文件类型或无状态筛选的常规查询
            var whereSql = me.GetWhereSql({
                treeId: treeId,
                startDate: startDate,
                endDate: endDate,
                fileType: type
            });

            if (isAllData) {
                // 不分页，直接查询所有数据
                querySql = "select ID,TABLENAME, FILE_NUMBER,FILE_TYPE,FILE_NAME,FILEPATH,FILE_FORMAT,NODECODE,GATHERING_METHOD,SOURCE_SYSTEM from RESULTGATHER"
                    + whereSql + "order by CREATE_TIMESTAMP desc";
            } else {
                // 分页查询
                querySql = "select * from (select s.* ,ROWNUM rowno from " +
                    "(select ID,TABLENAME, FILE_NUMBER,FILE_TYPE,FILE_NAME,FILEPATH,FILE_FORMAT,NODECODE,GATHERING_METHOD,SOURCE_SYSTEM from RESULTGATHER"
                    + whereSql + "order by CREATE_TIMESTAMP desc) s ) where  rowno &gt;= " + startRowno + " and  rowno &lt;=" + endRowno;
            }
            
            // 使用标准计数SQL
            countSql = me.GetQueryCountSql({
                treeId: treeId,
                startDate: startDate,
                endDate: endDate,
                fileType: type
            });
        }
        
        // 执行计数查询
        var count = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql }).rows[0]["COUNT"];
        // 执行数据查询
        var pageDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: querySql }).ToJSON().rows;

        // 如果是现场问题处理单，且没有包含状态信息，则补充状态信息
        if (fileType.indexOf("现场问题处理单") &gt; -1 &amp;&amp; !isProblemChildType &amp;&amp; fileType.indexOf("|全部") === -1) {
            for (var i = 0; i &lt; pageDatas.length; i++) {
                var data = pageDatas[i];
                // 如果没有STATE字段，则获取
                if (!data.hasOwnProperty('STATE')) {
                    var techProblemSql = "select STATE from XMLDATA_TECHPROBLEM " +
                        "where SOURCE_ID like '%" + data.TABLENAME + "%' and RESULT_ID = '" + data.ID + "'";
                    var stateRs = Things['Thing.DB.Oracle'].RunQuery({ sql: techProblemSql });
                    
                    if (stateRs.rows.length &gt; 0) {
                        data.STATE = stateRs.rows[0]['STATE'];
                    } else {
                        data.STATE = null;
                    }
                }
                
                // 为"更改设计文件"或"更改工艺文件"类型补充完成状态
                if (fileType.indexOf("更改设计文件") &gt; -1 || fileType.indexOf("更改工艺文件") &gt; -1) {
                    // 从完成状态查询中获取统一的FILESTATUS字段
                    var childType = fileType.indexOf("更改设计文件") &gt; -1 ? "更改设计文件" : "更改工艺文件";
                    
                    // 使用GetProblemTypeStatusSql方法获取完成状态
                    if (me.hasOwnProperty('GetProblemTypeStatusSql')) {
                        var statusSql = me.GetProblemTypeStatusSql({
                            id: data.ID,
                            tableName: data.TABLENAME,
                            childType: childType
                        });
                        
                        var statusRs = Things['Thing.DB.Oracle'].RunQuery({ sql: statusSql });
                        if (statusRs.rows.length &gt; 0) {
                            data.FILESTATUS = statusRs.rows[0]['STATUS'];
                        } else {
                            data.FILESTATUS = "0"; // 默认未完成
                        }
                    } else {
                        // 如果没有专门的状态查询方法，使用硬编码逻辑
                        var fieldName = childType === "更改设计文件" ? 
                            "DESIGNFILESTATUS" : "TECHFILESTATUS";
                            
                        try {
                            var statusSql = "select " + 
                                (childType === "更改设计文件" ? 
                                    (fileType.indexOf("已完成") &gt; -1 ? "1" : "0") :
                                    (fileType.indexOf("已完成") &gt; -1 ? "1" : "0")
                                ) + " as STATUS from dual";
                                
                            var statusRs = Things['Thing.DB.Oracle'].RunQuery({ sql: statusSql });
                            data.FILESTATUS = statusRs.rows[0]['STATUS'];
                        } catch (e) {
                            // 默认值
                            data.FILESTATUS = fileType.indexOf("已完成") &gt; -1 ? "1" : "0";
                        }
                    }
                }
            }
        }

        //查询型号和阶段（过程节点）
        for (var i = 0; i &lt; pageDatas.length; i++) {
            var dataPackageId = pageDatas[i].NODECODE;
            var redTreeIdSql = "select REFTREEID from DATA_PACKAGE where ID=" + dataPackageId;
            var processNameResult = Things['Thing.DB.Oracle'].RunQuery({ sql: "select NODENAME from DATAPACKAGETREE where TREEID=(" + redTreeIdSql + ")" });
            var modelNameResult = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from phase_model where treeid=(select TREEID from DATAPACKAGETREE where NODETYPE='phase' start with  TREEID=(" + redTreeIdSql + ") connect by prior PARENTID=TREEID)" });
            
            if (processNameResult.rows.length &gt; 0) {
                pageDatas[i].processName = processNameResult.rows[0]['NODENAME'];
            }
            if (modelNameResult.rows.length &gt; 0) {
                pageDatas[i].modelName = modelNameResult.rows[0]['NODENAME'];
            }
        }

        res = {
            code: 0,
            msg: "成功",
            count: count,
            data: pageDatas
        };
    }
} catch (error) {
    res.code = -1;
    res.msg = "QueryList失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryModelListCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryModelListCount
 * @description   查询型号的单据数量 wanghq 2023年4月13日10:48:54
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 * @param    {STRING}    fileType        {"aspect.defaultValue":"现场问题处理单"}
 *
 * @returns    {JSON}
 */
var res = {};
try {

	var modelRs = me.GetUserRoleModel({
		username: username,
		isUseScreen: 1,
		treeId: treeId
	});


	//现场问题处理单的总数
	var total = 0;
	var datas = [];
	//没有查询条件的情况下查询统计数据库
	if (me.StrIsEmpty({
		str: startDate
	}) &amp;&amp; me.StrIsEmpty({
		str: endDate
	})) {
		var modelArr = [];
		for (var i = 0; i &lt; modelRs.data.length; i++) {
			modelArr.push(modelRs.data[i].TREEID);
		}
		var models = modelArr.join(",");
		var statRs = Things['Thing.DB.Oracle'].RunQuery({
			sql: "select a.MODEL_ID,a.STAT_COUNT,b.NODENAME from (select * from MODEL_STATISTICS where MODEL_ID in (" + models + ") and STAT_TYPE='" + fileType + "') a left join PHASE_MODEL b on a.MODEL_ID=b.TREEID"
		});
		for (var i = 0; i &lt; statRs.rows.length; i++) {
			var stat = statRs.rows[i];
			var modelId = stat.MODEL_ID;
			var modelName = stat.NODENAME;
			var modelCount = stat.STAT_COUNT;
			total += modelCount;
			var obj = {};
			obj.modelName = modelName;
			obj.modelId = modelId;
			obj.modelCount = modelCount;
			if (modelCount &gt; 0) {
				datas.push(obj);
			}
		}
	} else {
		for (var i = 0; i &lt; modelRs.data.length; i++) {
			var obj = {};
			var model = modelRs.data[i];
			var modelId = model.TREEID;
			var modelName = model.NODENAME;
			var countSql = me.GetQueryCountSql({
				treeId: modelId,
				startDate: startDate,
				endDate: endDate,
				fileType: fileType
			});
			var countRs = Things["Thing.DB.Oracle"].RunQuery({
				sql: countSql
			});
			var modelCount = countRs.rows[0].COUNT;
			total += modelCount;
			obj.modelName = modelName;
			obj.modelId = modelId;
			obj.modelCount = modelCount;
			if (modelCount &gt; 0) {
				datas.push(obj);
			}
		}
	}

	//降序排序
	datas.sort(function (x, y) {
		return y.modelCount - x.modelCount;
	});
	if (datas.length &lt;= 10) {
		for (var i = 0; i &lt;= 10; i++) {
			if (datas.length &lt; i) {
				datas.push({
					modelName: ' ',
					modelId: 0,
					modelCount: ''
				});
			}
		}
	}
	var modelNames = [],
		modelCounts = [];
	for (var i = 0; i &lt; datas.length; i++) {
		modelNames.push(datas[i].modelName + '~~~' + datas[i].modelId);
		modelCounts.push(datas[i].modelCount);
	}
	res.success = true;
	var option = me.GetListChartOption();
	option.xAxis.data = modelNames;
	option.series[0].data = modelCounts;
	option.dataZoom[0].show = modelNames.length &gt; 10;
	res.data = {
		names: modelNames,
		counts: modelCounts,
		total: total,
		option: option
	};
	res.msg = "成功";
} catch (error) {
	res.success = false;
	res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryModelPhaseAIT"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryModelPhaseAIT
 * @description   查询型号阶段的AIT过程节点以及型号阶段名称 datetime 2024年1月10日18:39:43
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    var aitNodes = [];
    var nodes = Things['Thing.DB.Oracle'].RunQuery({
        sql:
            "select * from DATAPACKAGETREE where PARENTID =(select TREEID from DATAPACKAGETREE where" +
            " (nodename like '%整星ait%' or nodename like '%整星AIT%') start with TREEID=" + treeId +
            " connect by prior TREEID=PARENTID) order by NODESORT"
    });
    for (var i = 0; i &lt; nodes.rows.length; i++) {
        aitNodes.push(nodes.rows[i].NODENAME);
    }

    var modelPhaseName = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from phase_model where treeid=" + treeId }).rows[0]['NODENAME'];
    res.success = true;
    res.data = {
        aitNodes: aitNodes,
        modelPhaseName: modelPhaseName
    };
    res.msg = "查询型号阶段的AIT过程节点以及型号阶段名称成功";
} catch (error) {
    res.success = false;
    var msg = "QueryModelPhaseAIT-查询型号阶段的AIT过程节点以及型号阶段名称失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryModelProcess"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryModelProcess
 * @description   查询型号进度 wanghq 2024年1月10日19:15:58
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var modelRs = me.QueryAllModel({ username: username, treeId: treeId });
    var modelCount = modelRs.data.length;
    var modelArr = [];
    for (var i = 0; i &lt; modelCount; i++) {
        modelArr.push(modelRs.data[i].TREEID);
    }
    var models = modelArr.join(",");

    var queryModelSql = " select * from PHASE_MODEL where treeid in (" + models + ")";
    var models = Things['Thing.DB.Oracle'].RunQuery({ sql: queryModelSql }).rows;
    var isOnlyModel = models.length == 1;
    var tempTreeId = 3;
    if (isOnlyModel) {
        tempTreeId = models[0]['TREEID'];
    }
    //查询出模板树 整星AIT数据包 下的过程节点
    var tempNodeSql = "select NODENAME " +
        "                 from DATAPACKAGETREE " +
        "                 where PARENTID = (select TREEID " +
        "                                   from DATAPACKAGETREE " +
        "                                   where (nodename like '%整星ait%' or nodename like '%整星AIT%') " +
        "                                   start with TREEID = " + tempTreeId + " " +
        "                                   connect by prior TREEID = PARENTID) " +
        "                 order by NODESORT";
    var tempNodeRs = Things['Thing.DB.Oracle'].RunQuery({ sql: tempNodeSql });

    var resDatas = [];
    for (var i = 0; i &lt; tempNodeRs.rows.length; i++) {
        var tempNodeName = tempNodeRs.rows[i]['NODENAME'];
        var tempNodeSort = i + 1;
        var tempNodeModels = [];
        for (var j = 0; j &lt; models.length; j++) {
            var model = models[j];
            var modelId = model['TREEID'];
            var modelName = model['NODENAME'];
            var modelNodeName = model['CURRENT_AIT_NODE'];
            if (tempNodeName == modelNodeName) {
                tempNodeModels.push(modelName + "~~~" + modelId);
            }
        }
        var tempNodeModlesNum = tempNodeModels.length;
        resDatas.push({
            "models": tempNodeModels,
            "num": tempNodeModlesNum,
            "name": tempNodeName,
            "sort": tempNodeSort,
            "imgCls": "default"
        });
    }
    if (isOnlyModel) {
        var currentSort = 0;
        for (var i = 0; i &lt; resDatas.length; i++) {
            var node = resDatas[i];
            var sort = node['sort'];
            if (node['models'].length == 1) {
                currentSort = sort;
                node['imgCls'] = "current";
                break;
            }
        }
        for (var i = 0; i &lt; resDatas.length; i++) {
            var node = resDatas[i];
            var sort = node['sort'];
            if (currentSort &gt; sort) {
                node['imgCls'] = "complete";
            } else if (currentSort &lt; sort) {
                node['imgCls'] = "incomplete";
            }
        }
    }

    res.success = true;
    res.data = {
        isOnlyModel: isOnlyModel,
        datas: resDatas
    };
    res.msg = "查询型号进度成功";

} catch (error) {
    res.success = false;
    res.msg = "查询型号进度失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryModelProcessFromMes"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryModelProcessFromMes
 * @description   从Mes查询查询型号进度 wanghq 2025年4月8日21:32:47
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    username    
 *
 * @returns    {JSON}
 */
var res = {};
try {


    var modelRs = me.QueryAllModel({ username: username, treeId: treeId });
    var modelCount = modelRs.data.length;
    var modelArr = [];
    for (var i = 0; i &lt; modelCount; i++) {
        modelArr.push(modelRs.data[i].TREEID);
    }
    var models = modelArr.join(",");

    var queryModelSql = "select TREEID, NODENAME, CURRENT_AIT_NODE from (select a.TREEID, a.NODENAME, b.DEVELOP_STAGE as CURRENT_AIT_NODE from PHASE_MODEL a left join MES_MODEL_PROGRESS b on a.MODEL_CODE = b.MODEL_CODE and a.PHASE_CODE = b.CODE where a.treeid in(" + models + ")) where CURRENT_AIT_NODE !='其他' group by TREEID, NODENAME, CURRENT_AIT_NODE";
    var models = Things['Thing.DB.Oracle'].RunQuery({ sql: queryModelSql }).rows;
    var isOnlyModel = models.length == 1;
    var tempTreeId = 3;
    if (isOnlyModel) {
        tempTreeId = models[0]['TREEID'];
    }
    //查询出模板树 整星AIT数据包 下的过程节点
    var tempNodeSql = "select NODENAME " +
        "                 from DATAPACKAGETREE " +
        "                 where PARENTID = (select TREEID " +
        "                                   from DATAPACKAGETREE " +
        "                                   where (nodename like '%整星ait%' or nodename like '%整星AIT%') " +
        "                                   start with TREEID = " + tempTreeId + " " +
        "                                   connect by prior TREEID = PARENTID) " +
        "                 order by NODESORT";
    var tempNodeRs = Things['Thing.DB.Oracle'].RunQuery({ sql: tempNodeSql });

    var resDatas = [];
    for (var i = 0; i &lt; tempNodeRs.rows.length; i++) {
        var tempNodeName = tempNodeRs.rows[i]['NODENAME'];
        var tempNodeSort = i + 1;
        var tempNodeModels = [];
        for (var j = 0; j &lt; models.length; j++) {
            var model = models[j];
            var modelId = model['TREEID'];
            var modelName = model['NODENAME'];
            var modelNodeName = model['CURRENT_AIT_NODE'];
            if (tempNodeName == modelNodeName) {
                tempNodeModels.push(modelName + "~~~" + modelId);
            }
        }
        var tempNodeModlesNum = tempNodeModels.length;
        resDatas.push({
            "models": tempNodeModels,
            "num": tempNodeModlesNum,
            "name": tempNodeName,
            "sort": tempNodeSort,
            "imgCls": "default"
        });
    }
    if (isOnlyModel) {
        var currentSort = 0;
        for (var i = 0; i &lt; resDatas.length; i++) {
            var node = resDatas[i];
            var sort = node['sort'];
            if (node['models'].length == 1) {
                currentSort = sort;
                node['imgCls'] = "current";
                break;
            }
        }
        for (var i = 0; i &lt; resDatas.length; i++) {
            var node = resDatas[i];
            var sort = node['sort'];
            if (currentSort &gt; sort) {
                node['imgCls'] = "complete";
            } else if (currentSort &lt; sort) {
                node['imgCls'] = "incomplete";
            }
        }
    }

    res.success = true;
    res.data = {
        isOnlyModel: isOnlyModel,
        datas: resDatas
    };
    res.msg = "查询型号进度成功";

} catch (error) {
    res.success = false;
    res.msg = "查询型号进度失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryModelSubmitCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryModelSubmitCount
 * @description   查询产品交接单的数量 wanghq 2023年4月2日00:02:30
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 * @param    {STRING}    groupType        {"aspect.defaultValue":"ISCERTIFICATE"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var fileType = "产品交接单";
    var nullText = "未知";


    var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });

    var total = 0; //所有的总数
    var groupTotal = {}; //各个分类的总数
    var groupNames = [];
    var groupNamesAlias = [];
    var groupRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select distinct nvl(" + groupType + ",'" + nullText + "') as GROUP_NAME from V_LATEST_PRODUCT_SUBMIT  where " + groupType + " is not null" });
    for (var i = 0; i &lt; groupRs.rows.length; i++) {
        var groupName = groupRs.rows[i]['GROUP_NAME'];
        groupTotal[groupName] = 0;
        groupNames.push(groupName);
        groupNamesAlias.push("'" + groupName + "' " + groupName);
    }

    var modelDatas = [];

    //没有查询条件的情况下查询统计数据库
    if (me.StrIsEmpty({ str: startDate }) &amp;&amp; me.StrIsEmpty({ str: endDate })) {
        var modelArr = [];
        for (var i = 0; i &lt; modelRs.data.length; i++) {
            modelArr.push(modelRs.data[i].TREEID);
        }
        var models = modelArr.join(",");
        var statParentType = fileType + "_" + groupType + "_";
        var querySql = "select * from (select a.MODEL_ID,a.STAT_COUNT,replace(a.STAT_TYPE,'" + statParentType + "','') as STAT_TYPE,b.NODENAME from (select * from MODEL_STATISTICS where MODEL_ID in (" + models + ") and STAT_TYPE like '" + statParentType + "%') a left join PHASE_MODEL b on a.MODEL_ID=b.TREEID) pivot (sum(STAT_COUNT) for stat_type in (" + groupNamesAlias.join(",") + "))";
        var statRs = Things['Thing.DB.Oracle'].RunQuery({ sql: querySql });

        for (var i = 0; i &lt; statRs.rows.length; i++) {
            var stat = statRs.rows[i];
            var modelId = stat.MODEL_ID;
            var modelName = stat.NODENAME;
            var modelCount = 0;
            var modelGroupCount = {};
            for (var j = 0; j &lt; groupNames.length; j++) {
                var groupName = groupNames[j];
                var groupCount = stat[groupName] || 0;
                groupTotal[groupName] = groupTotal[groupName] + groupCount;
                modelGroupCount[groupName] = groupCount;
                modelCount = modelCount + groupCount;
                total += modelCount;
            }
            var obj = {};
            obj.modelName = modelName;
            obj.modelId = modelId;
            obj.modelCount = modelCount;
            obj.modelGroupCount = modelGroupCount;
            if (modelCount &gt; 0) {
                modelDatas.push(obj);
            }
        }
    } else {
        for (var i = 0; i &lt; modelRs.data.length; i++) {
            var obj = {};
            var model = modelRs.data[i];
            var modelId = model.TREEID;
            var modelName = model.NODENAME;

            var submitListSql = me.GetSubmitListSql({
                treeId: modelId,
                startDate: startDate,
                endDate: endDate
            });

            var totalCountSql = "select count(*) as COUNT from (" + submitListSql + ")";

            var totalCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalCountSql });

            var totalGroupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;

            var totalGroupCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalGroupCountSql });
            var modelGroupCount = {};
            for (var x = 0; x &lt; groupNames.length; x++) {
                var allGroupName = groupNames[x];
                var allGroupCount = 0;
                for (var j = 0; j &lt; totalGroupCountRs.rows.length; j++) {
                    var group = totalGroupCountRs.rows[j];
                    var groupName = group["GROUP_NAME"];
                    if (groupName == allGroupName) {
                        allGroupCount = group["COUNT"];
                        break;
                    }
                }
                groupTotal[allGroupName] = groupTotal[allGroupName] + allGroupCount;
                modelGroupCount[allGroupName] = allGroupCount;
            }

            var totalCount = totalCountRs.rows[0].COUNT;
            total += totalCount;
            obj.modelName = modelName;
            obj.modelId = modelId;
            obj.modelCount = totalCount;
            obj.modelGroupCount = modelGroupCount;
            if (totalCount &gt; 0) {
                modelDatas.push(obj);
            }

        }
    }

    //降序排序
    modelDatas.sort(function (x, y) {
        return y.modelCount - x.modelCount;
    });

    if (modelDatas.length &lt;= 10) {
        for (var i = 0; i &lt;= 10; i++) {
            if (modelDatas.length &lt; i) {
                modelDatas.push({
                    modelName: '',
                    modelId: 0,
                    modelCount: '',
                    modelGroupCount: {}
                });
            }
        }
    }

    // if (modelDatas.length &gt; 10) {
    //     modelDatas = modelDatas.slice(0, 10);
    // }

    var modelNames = [], modelCounts = {};
    for (var j = 0; j &lt; groupNames.length; j++) {
        modelCounts[groupNames[j]] = [];
    }
    for (var i = 0; i &lt; modelDatas.length; i++) {
        modelNames.push(modelDatas[i].modelName + '~~~' + modelDatas[i].modelId);
        var modelGroupCount = modelDatas[i].modelGroupCount;
        for (var groupName in modelGroupCount) {
            modelCounts[groupName].push(modelGroupCount[groupName]);
        }
    }
    var optionRs = me.GetSubmitChartOption({
        itemCounts: modelCounts,
        itemNames: modelNames
    });

    var numText = "";
    for (var key in groupTotal) {
        numText += key + ":" + groupTotal[key] + " "
    }
    res.data = {
        names: modelNames,
        counts: modelCounts,
        option: optionRs.data,
        total: total,
        groupTotal: groupTotal,
        numText: numText
    };
    res.success = true;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryNonconformityCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    QueryNonconformityCount&#xD;
 * @description   查询不合格品审理单的数量 wanghq 2025年5月23日&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}&#xD;
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}&#xD;
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}&#xD;
 * @param    {STRING}    username        {"aspect.defaultValue":" "}&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
var res = {};&#xD;
try {&#xD;
    // 定义状态数据对象，用于存储各型号的完成/未完成状态数据&#xD;
    var statusData = {&#xD;
        finished: {},&#xD;
        unfinished: {},&#xD;
        severityCounts: {},&#xD;
        severityStatus: {},&#xD;
        modelMap: {}  // 添加modelMap用于存储型号名称到ID的映射&#xD;
    };&#xD;
&#xD;
    // 获取固定的严重程度配置&#xD;
    var severityLevels = me.GetSeverityLevels().data;&#xD;
&#xD;
    // 初始化严重程度计数对象和状态对象&#xD;
    for (var i = 0; i &lt; severityLevels.length; i++) {&#xD;
        var severityLevel = severityLevels[i];&#xD;
        statusData.severityCounts[severityLevel] = {};&#xD;
&#xD;
        // 初始化严重程度状态对象&#xD;
        statusData.severityStatus[severityLevel] = {&#xD;
            finished: {},&#xD;
            unfinished: {}&#xD;
        };&#xD;
    }&#xD;
&#xD;
    var datas = [];&#xD;
&#xD;
    // 查询型号列表&#xD;
    var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });&#xD;
&#xD;
    // 构建型号ID列表&#xD;
    var modelArr = [];&#xD;
    for (var i = 0; i &lt; modelRs.data.length; i++) {&#xD;
        modelArr.push(modelRs.data[i].TREEID);&#xD;
    }&#xD;
&#xD;
    if (modelArr.length &gt; 0) {&#xD;
        // 将型号ID列表转为逗号分隔的字符串&#xD;
        var models = modelArr.join(",");&#xD;
&#xD;
        // 查询统计数据&#xD;
        var countSql = "SELECT n.NODENAME, m.MODEL_ID, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '不合格品审理单' THEN m.STAT_COUNT END) AS total_count, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '不合格品审理单~已完成' THEN m.STAT_COUNT END) AS finished_count, " +&#xD;
            "       SUM(CASE WHEN m.STAT_TYPE = '不合格品审理单~未完成' THEN m.STAT_COUNT END) AS unfinished_count";&#xD;
&#xD;
        // 添加各种严重程度的统计&#xD;
        for (var i = 0; i &lt; severityLevels.length; i++) {&#xD;
            var severityLevel = severityLevels[i];&#xD;
            var safeFieldName = "SEV_" + i;&#xD;
&#xD;
            countSql += ", SUM(CASE WHEN m.STAT_TYPE = '不合格品审理单_" + severityLevel + "' THEN m.STAT_COUNT END) AS " +&#xD;
                safeFieldName + "_count";&#xD;
&#xD;
            countSql += ", SUM(CASE WHEN m.STAT_TYPE = '不合格品审理单_" + severityLevel + "~已完成' THEN m.STAT_COUNT END) AS " +&#xD;
                safeFieldName + "_finished_count";&#xD;
&#xD;
            countSql += ", SUM(CASE WHEN m.STAT_TYPE = '不合格品审理单_" + severityLevel + "~未完成' THEN m.STAT_COUNT END) AS " +&#xD;
                safeFieldName + "_unfinished_count";&#xD;
        }&#xD;
&#xD;
        countSql += " FROM MODEL_STATISTICS m " +&#xD;
            "         LEFT JOIN PHASE_MODEL n ON m.MODEL_ID = n.TREEID " +&#xD;
            "WHERE m.MODEL_ID IN (" + models + ") " +&#xD;
            "GROUP BY n.NODENAME, m.MODEL_ID";&#xD;
&#xD;
        var statRs = Things['Thing.DB.Oracle'].RunQuery({ sql: countSql });&#xD;
&#xD;
        for (var i = 0; i &lt; statRs.rows.length; i++) {&#xD;
            var stat = statRs.rows[i];&#xD;
            var modelId = stat.MODEL_ID;&#xD;
            var modelName = stat.NODENAME;&#xD;
            var totalCount = stat.TOTAL_COUNT || 0;&#xD;
            var finishedCount = stat.FINISHED_COUNT || 0;&#xD;
            var unfinishedCount = stat.UNFINISHED_COUNT || 0;&#xD;
&#xD;
            statusData.finished[modelId] = finishedCount;&#xD;
            statusData.unfinished[modelId] = unfinishedCount;&#xD;
            statusData.modelMap[modelName] = modelId;&#xD;
&#xD;
            for (var j = 0; j &lt; severityLevels.length; j++) {&#xD;
                var severityLevel = severityLevels[j];&#xD;
                var safeFieldName = "SEV_" + j;&#xD;
                var countField = (safeFieldName + "_COUNT").toUpperCase();&#xD;
                var severityCount = stat[countField] || 0;&#xD;
                statusData.severityCounts[severityLevel][modelId] = severityCount;&#xD;
&#xD;
                var finishedField = (safeFieldName + "_FINISHED_COUNT").toUpperCase();&#xD;
                var severityFinishedCount = stat[finishedField] || 0;&#xD;
                statusData.severityStatus[severityLevel].finished[modelId] = severityFinishedCount;&#xD;
&#xD;
                var unfinishedField = (safeFieldName + "_UNFINISHED_COUNT").toUpperCase();&#xD;
                var severityUnfinishedCount = stat[unfinishedField] || 0;&#xD;
                statusData.severityStatus[severityLevel].unfinished[modelId] = severityUnfinishedCount;&#xD;
            }&#xD;
&#xD;
            if (totalCount &gt; 0) {&#xD;
                var obj = {&#xD;
                    name: modelName + "~~~" + modelId,&#xD;
                    value: [totalCount]&#xD;
                };&#xD;
&#xD;
                for (var j = 0; j &lt; severityLevels.length; j++) {&#xD;
                    var countField = ("SEV_" + j + "_COUNT").toUpperCase();&#xD;
                    var severityCount = stat[countField] || 0;&#xD;
                    obj.value.push(severityCount);&#xD;
                }&#xD;
&#xD;
                datas.push(obj);&#xD;
            }&#xD;
        }&#xD;
    }&#xD;
&#xD;
    // 为不同严重程度定义渐变颜色&#xD;
    var gradientColors = [&#xD;
        { start: '#F56C6C', end: '#C45656' },  // 一级 - 红色&#xD;
        { start: '#E6A23C', end: '#B58030' },  // 二级 - 橙色&#xD;
        { start: '#67C23A', end: '#529A2E' }   // 三级 - 绿色&#xD;
    ];&#xD;
&#xD;
    var option = me.GetNonconformityChartOption();&#xD;
    var dataset = [['名称', '总数量']];&#xD;
&#xD;
    for (var i = 0; i &lt; severityLevels.length; i++) {&#xD;
        dataset[0].push(severityLevels[i]);&#xD;
    }&#xD;
    // 按照总数量（value[0]）从大到小排序&#xD;
    if (datas.length &gt; 0) {&#xD;
        datas.sort(function (a, b) {&#xD;
            return b.value[0] - a.value[0]; // 降序排序&#xD;
        });&#xD;
    }&#xD;
&#xD;
    for (var i = 0; i &lt; datas.length; i++) {&#xD;
        var row = [datas[i].name];&#xD;
        for (var j = 0; j &lt; datas[i].value.length; j++) {&#xD;
            row.push(datas[i].value[j]);&#xD;
        }&#xD;
        dataset.push(row);&#xD;
    }&#xD;
&#xD;
    var series = [];&#xD;
&#xD;
    // 添加总数量系列&#xD;
    series.push({&#xD;
        name: '总数量',&#xD;
        type: 'bar',&#xD;
        barWidth: 20,&#xD;
        showBackground: false,&#xD;
        label: {&#xD;
            show: true,&#xD;
            position: 'top',&#xD;
            textStyle: {&#xD;
                color: 'white'&#xD;
            }&#xD;
        },&#xD;
        itemStyle: {&#xD;
            color: {&#xD;
                type: 'linear',&#xD;
                x: 0, y: 0, x2: 0, y2: 1,&#xD;
                colorStops: [{&#xD;
                    offset: 0,&#xD;
                    color: '#00CBD2'&#xD;
                }, {&#xD;
                    offset: 1,&#xD;
                    color: '#00A2FE'&#xD;
                }],&#xD;
                global: false&#xD;
            },&#xD;
            borderRadius: [20, 20, 0, 0]&#xD;
        }&#xD;
    });&#xD;
&#xD;
    // 添加各种严重程度的数据系列&#xD;
    for (var i = 0; i &lt; severityLevels.length; i++) {&#xD;
        // 选择颜色，使用取模运算确保不会超出颜色数组范围&#xD;
        var colorIndex = i % gradientColors.length;&#xD;
        var gradientColor = gradientColors[colorIndex];&#xD;
&#xD;
        series.push({&#xD;
            name: severityLevels[i],&#xD;
            type: 'bar',&#xD;
            barWidth: 20,&#xD;
            showBackground: false,&#xD;
            label: {&#xD;
                show: true,&#xD;
                position: 'top',&#xD;
                textStyle: {&#xD;
                    color: 'white'&#xD;
                }&#xD;
            },&#xD;
            itemStyle: {&#xD;
                color: {&#xD;
                    type: 'linear',&#xD;
                    x: 0, y: 0, x2: 0, y2: 1,&#xD;
                    colorStops: [{&#xD;
                        offset: 0,&#xD;
                        color: gradientColor.start&#xD;
                    }, {&#xD;
                        offset: 1,&#xD;
                        color: gradientColor.end&#xD;
                    }],&#xD;
                    global: false&#xD;
                },&#xD;
                borderRadius: [20, 20, 0, 0]&#xD;
            }&#xD;
        });&#xD;
    }&#xD;
&#xD;
    option.series = series;&#xD;
&#xD;
    // 设置数据集&#xD;
    option.dataset = {&#xD;
        source: dataset&#xD;
    };&#xD;
&#xD;
    // 计算各系列的总和&#xD;
    var totalObj = {};&#xD;
    // 初始化各系列总和&#xD;
    for (var i = 1; i &lt; dataset[0].length; i++) {&#xD;
        totalObj[dataset[0][i]] = 0;&#xD;
    }&#xD;
    // 累加各型号的数据&#xD;
    for (var i = 1; i &lt; dataset.length; i++) {&#xD;
        for (var j = 1; j &lt; dataset[i].length; j++) {&#xD;
            totalObj[dataset[0][j]] += dataset[i][j];&#xD;
        }&#xD;
    }&#xD;
&#xD;
    res.success = true;&#xD;
    res.data = {&#xD;
        option: option,&#xD;
        statusData: statusData,&#xD;
        total: totalObj&#xD;
    };&#xD;
    res.msg = "查询不合格品审理单统计数据成功";&#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    res.msg = "查询不合格品审理单统计数据失败，原因：" + error;&#xD;
}&#xD;
result = res;&#xD;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryNonconformityList"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryNonconformityList
 * @description   查询不合格品审理单列表 wanghq 2025年5月23日
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    status        {"aspect.defaultValue":"all"}
 * @param    {STRING}    severityLevel        {"aspect.defaultValue":"all"}
 * @param    {NUMBER}    page        {"aspect.defaultValue":"1.0"}
 * @param    {NUMBER}    limit        {"aspect.defaultValue":"10.0"}
 * @param    {BOOLEAN}    isAllData        {"aspect.defaultValue":"false"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 构建基础SQL查询
    var sql = "SELECT r.*, m.NODENAME AS MODEL_NAME FROM BPM_NONCONFORMITY_REVIEW r " +
              "LEFT JOIN PHASE_MODEL m ON r.XH = m.MODEL_NAME AND r.YZJD = m.PHASE_CODE WHERE 1=1";
    var countSql = "SELECT COUNT(*) AS COUNT FROM BPM_NONCONFORMITY_REVIEW r " +
                   "LEFT JOIN PHASE_MODEL m ON r.XH = m.MODEL_NAME AND r.YZJD = m.PHASE_CODE WHERE 1=1";
    
    // 添加型号筛选条件
    if (treeId != -1) {
        var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });
        
        if (modelRs.data.length &gt; 0) {
            var modelName = modelRs.data[0].MODEL_NAME;
            var phaseCode = modelRs.data[0].PHASE_CODE;
            sql += " AND r.XH = '" + modelName + "' AND r.YZJD = '" + phaseCode + "'";
            countSql += " AND r.XH = '" + modelName + "' AND r.YZJD = '" + phaseCode + "'";
        }
    }
    
    // 添加日期筛选条件
    if (startDate &amp;&amp; startDate.trim() !== "") {
        sql += " AND r.CREATE_TIME &gt;= TO_DATE('" + startDate + "', 'YYYY-MM-DD')";
        countSql += " AND r.CREATE_TIME &gt;= TO_DATE('" + startDate + "', 'YYYY-MM-DD')";
    }
    
    if (endDate &amp;&amp; endDate.trim() !== "") {
        sql += " AND r.CREATE_TIME &lt;= TO_DATE('" + endDate + " 23:59:59', 'YYYY-MM-DD HH24:MI:SS')";
        countSql += " AND r.CREATE_TIME &lt;= TO_DATE('" + endDate + " 23:59:59', 'YYYY-MM-DD HH24:MI:SS')";
    }
    
    // 添加状态筛选条件
    if (status &amp;&amp; status !== "all") {
        var statusValue = status === "finished" ? "1" : "0";
        sql += " AND r.ISEND = '" + statusValue + "'";
        countSql += " AND r.ISEND = '" + statusValue + "'";
    }
    
    // 添加严重程度筛选条件
    if (severityLevel &amp;&amp; severityLevel !== "all") {
        sql += " AND r.BHGPYZCD = '" + severityLevel + "'";
        countSql += " AND r.BHGPYZCD = '" + severityLevel + "'";
    }
    
    // 添加排序
    sql += " ORDER BY r.CREATE_TIME DESC";
    
    // 执行计数查询
    var countRs = Things['Thing.DB.Oracle'].RunQuery({ sql: countSql });
    var total = countRs.rows[0].COUNT;
    
    // 如果不是请求所有数据，添加分页
    if (!isAllData) {
        var offset = (page - 1) * limit;
        sql = "SELECT * FROM (" +
              "SELECT ROWNUM AS RN, t.* FROM (" + sql + ") t WHERE ROWNUM &lt;= " + (offset + limit) +
              ") WHERE RN &gt; " + offset;
    }
    
    // 执行数据查询
    var dataRs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql });
    
    // 转换查询结果为JSON数组
    var data = [];
    for (var i = 0; i &lt; dataRs.rows.length; i++) {
        var row = dataRs.rows[i];
        var item = {};
        
        // 复制所有字段
        for (var key in row) {
            item[key] = row[key];
        }
        
        // 格式化日期字段
        if (item.CREATE_TIME) {
            var createDate = new Date(item.CREATE_TIME);
            item.CREATE_DATE = dateFormat(createDate, "yyyy-MM-dd");
        } else {
            item.CREATE_DATE = "";
        }
        
        // 将ISEND字段的值转换为状态文本
        item.STATUS_TEXT = row.ISEND === "1" ? "已完成" : "未完成";
        
        data.push(item);
    }
    
    // 返回结果
    res.code = 0;
    res.msg = "查询成功";
    res.count = total;
    res.data = data;
    
} catch (error) {
    res.code = 500;
    res.msg = "查询不合格品审理单列表失败，原因：" + error;
    res.count = 0;
    res.data = [];
}
result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryProblemCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryProblemCount
 * @description   查询现场问题处理单的数量 wanghq 2024年1月12日15:26:09
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });

    //现场问题处理单的总数
    var total = 0, designTotal = 0, techTotal = 0;
    var datas = [];
    // 状态统计对象
    var statusData = {
        closedLoop: {},      // 已闭环
        openLoop: {},        // 未闭环
        designFinished: {},  // 设计文件已完成
        designUnfinished: {}, // 设计文件未完成
        techFinished: {},    // 工艺文件已完成
        techUnfinished: {}   // 工艺文件未完成
    };
    
    //没有查询条件的情况下查询统计数据库
    if (me.StrIsEmpty({ str: startDate }) &amp;&amp; me.StrIsEmpty({ str: endDate })) {
        var modelArr = [];
        for (var i = 0; i &lt; modelRs.data.length; i++) {
            modelArr.push(modelRs.data[i].TREEID);
        }
        var models = modelArr.join(",");
        var countSql = "SELECT n.NODENAME, " +
            "       m.MODEL_ID, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单' THEN m.STAT_COUNT END) AS whole, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单_更改设计文件' THEN m.STAT_COUNT END) AS design, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单_更改工艺文件' THEN m.STAT_COUNT END) AS tech, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单~已闭环' THEN m.STAT_COUNT END) AS closed_loop, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单~未闭环' THEN m.STAT_COUNT END) AS open_loop, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单_更改设计文件~已完成' THEN m.STAT_COUNT END) AS design_finished, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单_更改设计文件~未完成' THEN m.STAT_COUNT END) AS design_unfinished, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单_更改工艺文件~已完成' THEN m.STAT_COUNT END) AS tech_finished, " +
            "       SUM(CASE WHEN m.STAT_TYPE = '现场问题处理单_更改工艺文件~未完成' THEN m.STAT_COUNT END) AS tech_unfinished " +
            "FROM MODEL_STATISTICS m " +
            "         LEFT JOIN PHASE_MODEL n ON m.MODEL_ID = n.TREEID " +
            "WHERE m.MODEL_ID IN (" + models +
            ") AND m.STAT_TYPE IN ('现场问题处理单', '现场问题处理单_更改工艺文件', '现场问题处理单_更改设计文件', " +
            "'现场问题处理单~已闭环', '现场问题处理单~未闭环', " +
            "'现场问题处理单_更改设计文件~已完成', '现场问题处理单_更改设计文件~未完成', " + 
            "'现场问题处理单_更改工艺文件~已完成', '现场问题处理单_更改工艺文件~未完成') " +
            "GROUP BY n.NODENAME, m.MODEL_ID"
        var statRs = Things['Thing.DB.Oracle'].RunQuery({ sql: countSql });
        for (var i = 0; i &lt; statRs.rows.length; i++) {
            var stat = statRs.rows[i];
            var modelId = stat.MODEL_ID;
            var modelName = stat.NODENAME;
            var wholeCount = stat.WHOLE;
            var designCount = stat.DESIGN;
            var techCount = stat.TECH;
            var closedLoopCount = stat.CLOSED_LOOP || 0;
            var openLoopCount = stat.OPEN_LOOP || 0;
            var designFinishedCount = stat.DESIGN_FINISHED || 0;
            var designUnfinishedCount = stat.DESIGN_UNFINISHED || 0;
            var techFinishedCount = stat.TECH_FINISHED || 0;
            var techUnfinishedCount = stat.TECH_UNFINISHED || 0;

            total += wholeCount;
            designTotal += designCount;
            techTotal += techCount;
            var obj = {};
            obj.modelName = modelName;
            obj.modelId = modelId;
            obj.wholeCount = wholeCount;
            obj.designCount = designCount;
            obj.techCount = techCount;
            obj.closedLoopCount = closedLoopCount;
            obj.openLoopCount = openLoopCount;
            obj.designFinishedCount = designFinishedCount;
            obj.designUnfinishedCount = designUnfinishedCount;
            obj.techFinishedCount = techFinishedCount;
            obj.techUnfinishedCount = techUnfinishedCount;
            
            // 保存闭环状态数据
            statusData.closedLoop[modelId] = closedLoopCount;
            statusData.openLoop[modelId] = openLoopCount;
            statusData.designFinished[modelId] = designFinishedCount;
            statusData.designUnfinished[modelId] = designUnfinishedCount;
            statusData.techFinished[modelId] = techFinishedCount;
            statusData.techUnfinished[modelId] = techUnfinishedCount;
            
            if (wholeCount &gt; 0) {
                datas.push(obj);
            }
        }
    } else {
        for (var i = 0; i &lt; modelRs.data.length; i++) {
            var obj = {};
            var model = modelRs.data[i];
            var modelId = model.TREEID;
            var modelName = model.NODENAME;
            var countSql = me.GetQueryCountSql({
                treeId: modelId,
                startDate: startDate,
                endDate: endDate,
                fileType: "现场问题处理单"
            });
            var countRs = Things["Thing.DB.Oracle"].RunQuery({
                sql: countSql
            });
            var wholeCount = countRs.rows[0].COUNT;

            function getTypeCount(typeSql) {
                var whereSql = me.GetWhereSql({
                    treeId: modelId,
                    startDate: startDate,
                    endDate: endDate,
                    fileType: '现场问题处理单'
                });

                var countSql = "select count(*) as count " +
                    "from (select * from RESULTGATHER " + whereSql + ") m " +
                    "         left join XMLDATA_TECHPROBLEM n on n.SOURCE_ID like '%' || m.TABLENAME || '%' and n.RESULT_ID = m.ID " + typeSql;
                var statCount = Things['Thing.DB.Oracle'].RunQuery({ sql: countSql }).rows[0]['COUNT'];
                return statCount;
            }

            total += wholeCount;
            obj.modelName = modelName;
            obj.modelId = modelId;
            obj.wholeCount = wholeCount;
            obj.designCount = getTypeCount("where n.ISNEEDCHANGEDESIGNFILE = '1' or n.ISNEEDDEVIATEDESIGNFILE = '1'");
            obj.techCount = getTypeCount("where n.ISNEEDCHANGETECHFILE = '1' or n.ISNEEDDEVIATETECHFILE = '1'");
            
            // 获取闭环和未闭环数量
            obj.closedLoopCount = getTypeCount("where n.CLOSELOOPSTATUS = '1'");
            obj.openLoopCount = wholeCount - obj.closedLoopCount;
            
            // 获取设计文件已完成和未完成数量
            obj.designFinishedCount = getTypeCount("where (n.ISNEEDCHANGEDESIGNFILE = '1' or n.ISNEEDDEVIATEDESIGNFILE = '1') and n.DESIGNFILESTATUS = '1'");
            obj.designUnfinishedCount = obj.designCount - obj.designFinishedCount;
            
            // 获取工艺文件已完成和未完成数量
            obj.techFinishedCount = getTypeCount("where (n.ISNEEDCHANGETECHFILE = '1' or n.ISNEEDDEVIATETECHFILE = '1') and n.TECHFILESTATUS = '1'");
            obj.techUnfinishedCount = obj.techCount - obj.techFinishedCount;

            designTotal += obj.designCount;
            techTotal += obj.techCount;
            
            // 保存闭环状态数据
            statusData.closedLoop[modelId] = obj.closedLoopCount;
            statusData.openLoop[modelId] = obj.openLoopCount;
            statusData.designFinished[modelId] = obj.designFinishedCount;
            statusData.designUnfinished[modelId] = obj.designUnfinishedCount;
            statusData.techFinished[modelId] = obj.techFinishedCount;
            statusData.techUnfinished[modelId] = obj.techUnfinishedCount;
            
            if (wholeCount &gt; 0) {
                datas.push(obj);
            }
        }
    }

    //降序排序
    datas.sort(function (x, y) {
        return y.wholeCount - x.wholeCount;
    });
    if (datas.length &lt;= 10) {
        for (var i = 0; i &lt;= 10; i++) {
            if (datas.length &lt; i) {
                datas.push({
                    modelName: '',
                    modelId: i,
                    wholeCount: '',
                    designCount: '',
                    techCount: ''
                });
            }
        }
    }

    // if (datas.length &gt; 10) {
    //     datas = datas.slice(0, 10);
    // }
    var dataset = [['名称', '全部', '更改设计文件', '更改工艺文件']];
    for (var i = 0; i &lt; datas.length; i++) {
        dataset.push([datas[i].modelName + '~~~' + datas[i].modelId, datas[i].wholeCount, datas[i].designCount, datas[i].techCount]);
    }
    var option = me.GetProblemChartOption();
    option.dataZoom[0].show = datas.length &gt; 10;
    option.dataset.source = dataset;
    res.success = true;
    res.data = {
        option: option,
        total: {
            '全部': total,
            '更改设计文件': designTotal,
            '更改工艺文件': techTotal
        },
        // 添加闭环状态数据
        problemStatus: statusData
    };
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryProccessCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
 * @definition    QueryProccessCount&#xD;
 * @description   查询过程节点的数量 wanghq 2023年3月30日16:37:56&#xD;
 * @implementation    {Script}&#xD;
 *&#xD;
 * @param    {NUMBER}    modelId        {"aspect.defaultValue":"-1.0"}&#xD;
 * @param    {STRING}    startDate    &#xD;
 * @param    {STRING}    endDate    &#xD;
 * @param    {STRING}    fileType        {"aspect.defaultValue":"现场问题处理单"}&#xD;
 *&#xD;
 * @returns    {JSON}&#xD;
 */&#xD;
var res = {};&#xD;
try {&#xD;
    var datas = [];&#xD;
    if (fileType.indexOf('现场临时处理单') &gt; -1) {&#xD;
        var childType = fileType.split("|")[1];&#xD;
        var status = "";&#xD;
        &#xD;
        // 解析fileType参数，获取筛选状态&#xD;
        if (fileType.indexOf('|已完成') &gt; -1) {&#xD;
            status = "已完成";&#xD;
        } else if (fileType.indexOf('|未完成') &gt; -1) {&#xD;
            status = "未完成";&#xD;
        }&#xD;
        &#xD;
        // 构建查询条件&#xD;
        var query = {&#xD;
            startDate: startDate,&#xD;
            endDate: endDate,&#xD;
            category: childType,&#xD;
            treeId: modelId&#xD;
        };&#xD;
        &#xD;
        var baseSql = Things['Thing.Fn.ElectricTest'].GetTempSql({&#xD;
            query: query&#xD;
        }).data;&#xD;
        &#xD;
        // 构建完整SQL查询，正确应用状态过滤条件&#xD;
        var sql = "select LEFT_ID, LEFT_NAME, LEFT_SORT, count(*) as count from (" + baseSql + ")";&#xD;
        // 根据完成状态添加过滤条件&#xD;
        if (status === "已完成") {&#xD;
            sql += " WHERE IS_FINISHED = 1";&#xD;
        } else if (status === "未完成") {&#xD;
            sql += " WHERE (IS_FINISHED = 0 OR IS_FINISHED IS NULL)";&#xD;
        }&#xD;
        // 添加分组和排序&#xD;
        sql += " group by LEFT_ID, LEFT_NAME, LEFT_SORT order by LEFT_SORT";&#xD;
        &#xD;
        var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: sql });&#xD;
        for (var i = 0; i &lt; rs.rows.length; i++) {&#xD;
            var node = rs.rows[i];&#xD;
            var processId = node["LEFT_ID"];&#xD;
            var processName = node["LEFT_NAME"];&#xD;
            var processSort = node["LEFT_SORT"];&#xD;
            var processCount = node["COUNT"];&#xD;
            datas.push({&#xD;
                processId: processId,&#xD;
                processName: processName,&#xD;
                processSort: processSort,&#xD;
                processCount: processCount&#xD;
            });&#xD;
        }&#xD;
    } else {&#xD;
        var processSql = "select * from  DATAPACKAGETREE where PARENTID=" +&#xD;
            "(select TREEID from DATAPACKAGETREE where (nodename like '%整星ait%' or nodename like '%整星AIT%') " +&#xD;
            "and ROWNUM=1 start with TREEID = " + modelId + " connect by prior TREEID=PARENTID)  order by NODESORT";&#xD;
        var processRs = Things['Thing.DB.Oracle'].RunQuery({ sql: processSql });&#xD;
        &#xD;
        // 解析现场问题处理单的状态筛选条件&#xD;
        var statusFilter = "";&#xD;
        var childTypeFilter = "";&#xD;
        &#xD;
        if (fileType.indexOf('现场问题处理单') &gt; -1) {&#xD;
            var parts = fileType.split("|");&#xD;
            &#xD;
            if (parts.length &gt;= 2) {&#xD;
                childTypeFilter = parts[1];&#xD;
                &#xD;
                // 处理状态筛选条件&#xD;
                if (parts.length &gt;= 3) {&#xD;
                    var statusPart = parts[2];&#xD;
                    if (statusPart === '已闭环') {&#xD;
                        statusFilter = " AND n.STATE = '7'";&#xD;
                    } else if (statusPart === '未闭环') {&#xD;
                        statusFilter = " AND (n.STATE != '7' OR n.STATE IS NULL)";&#xD;
                    } else if (statusPart === '已完成') {&#xD;
                        if (childTypeFilter === '更改设计文件') {&#xD;
                            // 根据UpdateProblemCount.js的逻辑，使用me.GetProblemTypeCountSql&#xD;
                            statusFilter = " AND 已完成状态";  // 这个会被下面的代码逻辑替换，此处只是占位&#xD;
                        } else if (childTypeFilter === '更改工艺文件') {&#xD;
                            // 根据UpdateProblemCount.js的逻辑，使用me.GetProblemTypeCountSql&#xD;
                            statusFilter = " AND 已完成状态";  // 这个会被下面的代码逻辑替换，此处只是占位&#xD;
                        }&#xD;
                    } else if (statusPart === '未完成') {&#xD;
                        if (childTypeFilter === '更改设计文件') {&#xD;
                            // 根据UpdateProblemCount.js的逻辑，使用me.GetProblemTypeCountSql&#xD;
                            statusFilter = " AND 未完成状态";  // 这个会被下面的代码逻辑替换，此处只是占位&#xD;
                        } else if (childTypeFilter === '更改工艺文件') {&#xD;
                            // 根据UpdateProblemCount.js的逻辑，使用me.GetProblemTypeCountSql&#xD;
                            statusFilter = " AND 未完成状态";  // 这个会被下面的代码逻辑替换，此处只是占位&#xD;
                        }&#xD;
                    }&#xD;
                }&#xD;
            }&#xD;
        }&#xD;
        &#xD;
        for (var i = 0; i &lt; processRs.rows.length; i++) {&#xD;
            var process = processRs.rows[i];&#xD;
            var processId = process["TREEID"];&#xD;
            var processName = process["NODENAME"];&#xD;
            var processSort = process["NODESORT"];&#xD;
&#xD;
            var processCount = 0;&#xD;
            &#xD;
            // 处理不同类型的文件&#xD;
            if (fileType.indexOf('现场问题处理单') &gt; -1) {&#xD;
                var parts = fileType.split("|");&#xD;
                var childType = parts.length &gt;= 2 ? parts[1] : "";&#xD;
                var state = parts.length &gt;= 3 ? parts[2] : "";&#xD;
                &#xD;
                if (childType === '全部') {&#xD;
                    if (state === '已闭环' || state === '未闭环') {&#xD;
                        // 使用 UpdateModelTypeListCount.js 中的逻辑&#xD;
                        var whereSql = me.GetWhereSql({&#xD;
                            treeId: processId,&#xD;
                            startDate: startDate,&#xD;
                            endDate: endDate,&#xD;
                            fileType: '现场问题处理单'&#xD;
                        });&#xD;
                        &#xD;
                        var querySql = "select count(*) as COUNT " +&#xD;
                            "from (select * from RESULTGATHER " + whereSql + ") m " +&#xD;
                            "left join XMLDATA_TECHPROBLEM n on n.SOURCE_ID like '%' || m.TABLENAME || '%' and n.RESULT_ID = m.ID ";&#xD;
                        &#xD;
                        if (state === '已闭环') {&#xD;
                            querySql += " where n.STATE = '7'";&#xD;
                        } else if (state === '未闭环') {&#xD;
                            querySql += " where n.STATE != '7' OR n.STATE IS NULL";&#xD;
                        }&#xD;
                        &#xD;
                        var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: querySql });&#xD;
                        processCount = countRs.rows[0]["COUNT"];&#xD;
                    } else {&#xD;
                        // 查询全部的现场问题处理单&#xD;
                        var countSql = me.GetQueryCountSql({&#xD;
                            treeId: processId,&#xD;
                            startDate: startDate,&#xD;
                            endDate: endDate,&#xD;
                            fileType: '现场问题处理单'&#xD;
                        });&#xD;
                        var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql });&#xD;
                        processCount = countRs.rows[0]["COUNT"];&#xD;
                    }&#xD;
                } else if (childType === '更改设计文件' || childType === '更改工艺文件') {&#xD;
                    // 使用 UpdateProblemCount.js 中的 GetProblemTypeCountSql 逻辑&#xD;
                    var countSql = me.GetProblemTypeCountSql({&#xD;
                        treeId: processId,&#xD;
                        startDate: startDate,&#xD;
                        endDate: endDate,&#xD;
                        childType: childType,&#xD;
                        state: state&#xD;
                    });&#xD;
                    var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql });&#xD;
                    processCount = countRs.rows[0]["COUNT"];&#xD;
                } else {&#xD;
                    // 默认查询&#xD;
                    var countSql = me.GetQueryCountSql({&#xD;
                        treeId: processId,&#xD;
                        startDate: startDate,&#xD;
                        endDate: endDate,&#xD;
                        fileType: fileType&#xD;
                    });&#xD;
                    var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql });&#xD;
                    processCount = countRs.rows[0]["COUNT"];&#xD;
                }&#xD;
            } else {&#xD;
                // 其他类型文件的常规查询&#xD;
                var countSql = me.GetQueryCountSql({&#xD;
                    treeId: processId,&#xD;
                    startDate: startDate,&#xD;
                    endDate: endDate,&#xD;
                    fileType: fileType&#xD;
                });&#xD;
                var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql });&#xD;
                processCount = countRs.rows[0].COUNT;&#xD;
            }&#xD;
            &#xD;
            datas.push({&#xD;
                processId: processId,&#xD;
                processName: processName,&#xD;
                processSort: processSort,&#xD;
                processCount: processCount&#xD;
            });&#xD;
        }&#xD;
    }&#xD;
&#xD;
    res.success = true;&#xD;
    res.data = datas;&#xD;
    res.msg = "成功";&#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    res.msg = "失败，原因：" + error;&#xD;
}&#xD;
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryQualityInfo"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**&#xD;
* @definition    QueryQualityInfo&#xD;
* @description   查询质量信息看板数据    wanghq    2025年8月5日18:19:12&#xD;
* @implementation    {Script}&#xD;
*&#xD;
* @returns    {JSON}&#xD;
*/&#xD;
var res = {};&#xD;
try {&#xD;
    var data = {&#xD;
        qualityProblem: {&#xD;
            "groupCount": 0,&#xD;
            "instituteCount": 0,&#xD;
            "factoryCount": 0,&#xD;
            "otherCount": 0,&#xD;
            "qualityProblemTotalCount": 0&#xD;
        },&#xD;
        testEvent: {&#xD;
            "starProductCount": 0,&#xD;
            "testDesignCount": 0,&#xD;
            "testImplementationCount": 0,&#xD;
            "telemetryAlarmCount": 0,&#xD;
            "testEventTotalCount": 0,&#xD;
        },&#xD;
        nqc: {&#xD;
            "reworkCount": 0,&#xD;
            "waiverCount": 0,&#xD;
            "downgradeCount": 0,&#xD;
            "scrapCount": 0,&#xD;
            "nqcTotalCount": 0,&#xD;
        },&#xD;
        temp: {&#xD;
            "assemblyCount": 0,&#xD;
            "testCount": 0,&#xD;
            "tempTotalCount": 0,&#xD;
        }&#xD;
&#xD;
    };&#xD;
&#xD;
    //查询质量问题数据&#xD;
    // var qualityProblemData = Things['Thing.Fn.BPM'].GetQualityProblemData();&#xD;
    // if (qualityProblemData.success) {&#xD;
    //     data.qualityProblem.groupCount = qualityProblemData.data.groupCount;&#xD;
    //     data.qualityProblem.instituteCount = qualityProblemData.data.instituteCount;&#xD;
    //     data.qualityProblem.factoryCount = qualityProblemData.data.factoryCount;&#xD;
    //     data.qualityProblem.otherCount = qualityProblemData.data.otherCount;&#xD;
    //     data.qualityProblem.qualityProblemTotalCount = qualityProblemData.data.totalCount;&#xD;
    // }&#xD;
&#xD;
    //查询测试异常数据&#xD;
    var testEventData = Things['Thing.DB.Oracle'].RunQuery({ sql: "SELECT SUM(CASE WHEN PROBLEM_CATEGORY = '星上产品' THEN 1 ELSE 0 END) AS 星上产品, SUM(CASE WHEN PROBLEM_CATEGORY = '测试设计' THEN 1 ELSE 0 END) AS 测试设计, SUM(CASE WHEN PROBLEM_CATEGORY = '测试实施' THEN 1 ELSE 0 END) AS 测试实施, SUM(CASE WHEN PROBLEM_CATEGORY = '遥测虚警' THEN 1 ELSE 0 END) AS 遥测虚警, COUNT(*) AS 总计 FROM MODEL_TEST_EVENTS WHERE PROBLEM_CATEGORY IN ('星上产品', '测试设计', '测试实施', '遥测虚警')" });&#xD;
    data.testEvent.starProductCount = testEventData.rows[0]['星上产品'];&#xD;
    data.testEvent.testDesignCount = testEventData.rows[0]['测试设计'];&#xD;
    data.testEvent.testImplementationCount = testEventData.rows[0]['测试实施'];&#xD;
    data.testEvent.telemetryAlarmCount = testEventData.rows[0]['遥测虚警'];&#xD;
    data.testEvent.testEventTotalCount = testEventData.rows[0]['总计'];&#xD;
&#xD;
    //查询不合格品审理单数据&#xD;
    var nqcData = Things['Thing.DB.Oracle'].RunQuery({ sql: "SELECT SUM(CASE WHEN BHGPSLYJ = '返工(返修)' THEN 1 ELSE 0 END) AS 返工返修, SUM(CASE WHEN BHGPSLYJ = '让步接收' THEN 1 ELSE 0 END) AS 让步接收, SUM(CASE WHEN BHGPSLYJ = '降级使用' THEN 1 ELSE 0 END) AS 降级使用, SUM(CASE WHEN BHGPSLYJ = '报废' THEN 1 ELSE 0 END) AS 报废, COUNT(*) AS 总计 FROM BPM_NONCONFORMITY_REVIEW WHERE BHGPSLYJ IN ('返工(返修)', '让步接收', '降级使用', '报废')" });&#xD;
    data.nqc.reworkCount = nqcData.rows[0]['返工返修'];&#xD;
    data.nqc.waiverCount = nqcData.rows[0]['让步接收'];&#xD;
    data.nqc.downgradeCount = nqcData.rows[0]['降级使用'];&#xD;
    data.nqc.scrapCount = nqcData.rows[0]['报废'];&#xD;
    data.nqc.nqcTotalCount = nqcData.rows[0]['总计'];&#xD;
&#xD;
    //查询现场临时处理监控数据&#xD;
    var assemblyData = Things['Thing.DB.Oracle'].RunQuery({ sql: "select COUNT(*) COUNT from PROCESS_CONTROL_RESULT where FILE_TYPE = '现场问题处理单'" });&#xD;
    data.temp.assemblyCount = assemblyData.rows[0]['COUNT'];&#xD;
&#xD;
    var tempData = Things['Thing.DB.Oracle'].RunQuery({ sql: "select COUNT(*) COUNT from OTP_ORDER" });&#xD;
    data.temp.testCount = tempData.rows[0]['COUNT'];&#xD;
    data.temp.tempTotalCount = data.temp.assemblyCount + data.temp.testCount;&#xD;
&#xD;
    res.success = true;&#xD;
    res.data = data;&#xD;
    res.msg = "成功";&#xD;
} catch (error) {&#xD;
    res.success = false;&#xD;
    res.msg = "失败，原因：" + error;&#xD;
    logger.error(res.msg);&#xD;
}&#xD;
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QuerySubmitCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QuerySubmitCount
 * @description   查询产品交接单的数量 wanghq 2023年4月2日00:02:30
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 * @param    {STRING}    groupType        {"aspect.defaultValue":"ISCERTIFICATE"}
 *
 * @returns    {JSON}
 */
var res = {};
try {


    /**
    * 获取数组的下标
    * @param arr
    * @param val
    * @returns {number}
    */
    function indexOf(arr, val) {
        for (var i = 0; i &lt; arr.length; i++) {
            if (arr[i] == val) {
                return i;
            }
        }
        return -1;
    }
    /**
     * 判断一个元素是否在一个数组中
     * @param arr
     * @param val
     * @returns {boolean}
     */
    function contains(arr, val) {
        return indexOf(arr, val) != -1 ? true : false;
    }
    var nullText = "未知";

    var colors = ["#00CBD2", "#00B7E8", "#00A2FE"];

    var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });

    var total = 0;
    var groupTotal = {};
    var groupRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select distinct nvl(" + groupType + ",'" + nullText + "') as GROUP_NAME from V_LATEST_PRODUCT_SUBMIT" });
    for (var i = 0; i &lt; groupRs.rows.length; i++) {
        var groupName = groupRs.rows[i]['GROUP_NAME'];
        groupTotal[groupName] = 0;
    }
    var modelDatas = [];

    //查询总数
    for (var i = 0; i &lt; modelRs.data.length; i++) {
        var obj = {};
        var model = modelRs.data[i];
        var modelId = model.TREEID;
        var modelName = model.NODENAME;

        var submitListSql = me.GetSubmitListSql({
            treeId: modelId,
            startDate: startDate,
            endDate: endDate
        });

        var totalCountSql = "select count(*) as COUNT from (" + submitListSql + ")";

        var totalCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalCountSql });

        var totalGroupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;

        var totalGroupCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalGroupCountSql });

        for (var j = 0; j &lt; totalGroupCountRs.rows.length; j++) {
            var group = totalGroupCountRs.rows[j];
            var groupName = group["GROUP_NAME"];
            var groupCount = group["COUNT"];
            groupTotal[groupName] = groupTotal[groupName] + groupCount;
        }

        var totalCount = totalCountRs.rows[0].COUNT;
        total += totalCount;
        obj.modelName = modelName;
        obj.modelId = modelId;
        obj.modelCount = totalCount;
        modelDatas.push(obj);
    }

    //降序排序
    modelDatas.sort(function (x, y) {
        return y.modelCount - x.modelCount;
    });
    if (modelDatas.length &gt; 10) {
        modelDatas = modelDatas.slice(0, 10);
    }

    var gropuTypes = [];
    //查询分类数量
    for (var i = 0; i &lt; modelDatas.length; i++) {
        var model = modelDatas[i];
        var modelId = model.modelId;
        var modelName = model.modelName;

        var submitListSql = me.GetSubmitListSql({
            treeId: modelId,
            startDate: startDate,
            endDate: endDate
        });
        var groupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;
        var groupCountRs = Things["Thing.DB.Oracle"].RunQuery({
            sql: groupCountSql
        });
        var modelGroups = [];
        for (var j = 0; j &lt; groupCountRs.rows.length; j++) {
            var group = groupCountRs.rows[j];
            var groupName = group["GROUP_NAME"];
            if (!contains(gropuTypes, groupName)) {
                gropuTypes.push(groupName);
            }
            var groupCount = group["COUNT"];
            modelGroups.push({
                groupName: groupName,
                groupCount: groupCount
            });
        }
        model.modelGroups = modelGroups;
    }

    var modelNames = [], modelCounts = {};
    for (var i = 0; i &lt; gropuTypes.length; i++) {
        modelCounts[gropuTypes[i]] = [];
    }
    for (var i = 0; i &lt; modelDatas.length; i++) {
        modelNames.push(modelDatas[i].modelName + '~~~' + modelDatas[i].modelId);
        var modelGroups = modelDatas[i].modelGroups;
        for (var j = 0; j &lt; modelGroups.length; j++) {
            var groupName = modelGroups[j].groupName;
            var groupCount = modelGroups[j].groupCount;
            modelCounts[groupName].push(groupCount);
        }
    }
    var series = [], seriesIndex = 0;
    for (var key in modelCounts) {
        var s = {
            name: key,
            data: modelCounts[key],
            type: 'bar',
            showBackground: true,
            stack: 'total',
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            label: {
                show: true,
                textStyle: {
                    color: 'white'
                }
            },
            // itemStyle: {
            //     color: colors[seriesIndex]
            // },
            barWidth: 40
        };
        series.push(s);
        seriesIndex++;
    }

    if (seriesIndex == 0) {
        series.push({
            data: [0],
            type: 'bar',
            showBackground: true,
            stack: 'total',
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            label: {
                show: true,
                textStyle: {
                    color: 'white'
                }
            },
            barWidth: 40
        });
    }
    var option = {
        tooltip: {
        },
        legend: {
            textStyle: {
                color: "#fff"
            }
        },
        xAxis: {
            type: 'category',
            data: modelNames,
            axisLabel: {
                rotate: 30,
            },
            axisLine: {
                lineStyle: {
                    color: 'white'
                }
            }
        },
        yAxis: {
            show: true,
            // 设置坐标轴的样式
            axisLine: {
                lineStyle: {
                    color: 'white'
                }
            },
            axisLabel: {
                fontSize: 14
            },
            type: 'value',
            splitLine: {
                show: true,
                lineStyle: {
                    color: "#141D54"
                }
            }
        },
        series: series
    };

    var numText = "";
    for (var key in groupTotal) {
        numText += key + ":" + groupTotal[key] + " "
    }
    res.data = {
        names: modelNames,
        counts: modelCounts,
        option: option,
        total: total,
        groupTotal: groupTotal,
        numText: numText
    };
    res.success = true;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QuerySubmitList"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QuerySubmitList
 * @description   查询产品交接单列表 wanghq 2023年3月30日23:04:04
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 * @param    {STRING}    groupType    
 * @param    {INTEGER}    page        {"aspect.defaultValue":"1"}
 * @param    {INTEGER}    limit        {"aspect.defaultValue":"10"}
 * @param    {STRING}    unit        {"aspect.defaultValue":" "}
 * @param    {STRING}    seriesName    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var unitCol = "SUBMITUNIT";

    var submitListSql = me.GetSubmitListSql({
        treeId: treeId,
        startDate: startDate,
        endDate: endDate
    });

    submitListSql = "select * from (" + submitListSql + ") where 1=1";
    if (!me.StrIsEmpty({ str: unit })) {
        submitListSql += " and " + unitCol + "='" + unit + "'";
    }

    if (!me.StrIsEmpty({ str: seriesName })) {
        submitListSql += " and " + groupType + "='" + seriesName + "'";
    }
    
    var count = 0;
    var pageDatas = [];
    
    // 根据limit的值决定是否分页
    if (limit === 0) {
        // 不分页，查询所有数据
        pageDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: submitListSql }).ToJSON().rows;
        count = pageDatas.length;
    } else {
        // 进行分页查询
        var startRowno = (page - 1) * limit + 1;
        var endRowno = page * limit;
        
        var countSql = "select count(*) count from (" + submitListSql + ")";
        count = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql }).rows[0]["COUNT"];

        var querySql = "select * from (select s.* ,ROWNUM rowno from (" + submitListSql + ") s ) where  rowno &gt;= " + startRowno + " and  rowno &lt;=" + endRowno;
        pageDatas = Things["Thing.DB.Oracle"].RunQuery({ sql: querySql }).ToJSON().rows;
    }

    //查询型号和阶段（过程节点）
    for (var i = 0; i &lt; pageDatas.length; i++) {
        var dataPackageId = pageDatas[i].NODECODE;
        var refTreeIdSql = "select REFTREEID from DATA_PACKAGE where ID=" + dataPackageId;
        var processName = Things['Thing.DB.Oracle'].RunQuery({ sql: "select NODENAME from DATAPACKAGETREE where TREEID=(" + refTreeIdSql + ")" }).rows[0]['NODENAME'];
        var modelName = Things['Thing.DB.Oracle'].RunQuery({ sql: "select * from phase_model where treeid=(select treeid from DATAPACKAGETREE where NODETYPE='phase' start with TREEID=(" + refTreeIdSql + ") connect by prior PARENTID=TREEID)" }).rows[0]['NODENAME'];
        pageDatas[i].processName = processName;
        pageDatas[i].modelName = modelName;
    }

    res = {
        code: 0,
        msg: "成功",
        count: count,
        data: pageDatas
    };
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QuerySubmitUnitCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QuerySubmitUnitCount
 * @description   查询产品交接单的提交单位的数量 wanghq 2023年4月2日00:02:30
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    groupType        {"aspect.defaultValue":"ISCERTIFICATE"}
 * @param    {STRING}    seriesName    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var unitCol = "SUBMITUNIT";

    var submitListSql = me.GetSubmitListSql({
        treeId: treeId,
        startDate: startDate,
        endDate: endDate
    });

    if (!me.StrIsEmpty({ str: seriesName })) {
        submitListSql = "select * from(" + submitListSql + ") where " + groupType + "='" + seriesName + "'";
    }

    var nullText = "未知";
    //查询该型号下的分类名称
    var groupRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select distinct nvl(" + groupType + ",'" + nullText + "') as GROUP_NAME from (" + submitListSql + ")" });
    var groupNames = [];
    for (var i = 0; i &lt; groupRs.rows.length; i++) {
        var groupName = groupRs.rows[i]['GROUP_NAME'];
        groupNames.push(groupName);
    }

    //查询提交单位的数量
    var allUnitSql = "select s." + unitCol + " as UNIT, count(*) as COUNT from (" + submitListSql + ") s group by s." + unitCol;
    var allUnitRs = Things['Thing.DB.Oracle'].RunQuery({ sql: allUnitSql });


    var unitDatas = [];
    var gropuTypes = [];

    for (var i = 0; i &lt; allUnitRs.rows.length; i++) {
        var unit = allUnitRs.rows[i]['UNIT'];
        var groupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s where " + unitCol + "='" + unit + "' group by s." + groupType;
        var groupCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: groupCountSql });

        var unitGroupCount = {}, unitCount = 0;

        for (var x = 0; x &lt; groupNames.length; x++) {
            var allGroupName = groupNames[x];
            var allGroupCount = 0;
            for (var j = 0; j &lt; groupCountRs.rows.length; j++) {
                var group = groupCountRs.rows[j];
                var groupName = group["GROUP_NAME"];
                if (groupName == allGroupName) {
                    allGroupCount = group["COUNT"];
                    break;
                }
            }
            unitGroupCount[allGroupName] = allGroupCount;
            unitCount += allGroupCount;
        }
        var obj = {};
        obj.unit = unit;
        obj.unitGroupCount = unitGroupCount;
        obj.unitCount = unitCount;
        unitDatas.push(obj);
    }

    //降序排序
    unitDatas.sort(function (x, y) {
        return y.unitCount - x.unitCount;
    });

    var unitNames = [], unitCounts = {};
    for (var j = 0; j &lt; groupNames.length; j++) {
        unitCounts[groupNames[j]] = [];
    }
    for (var i = 0; i &lt; unitDatas.length; i++) {
        unitNames.push(unitDatas[i].unit);
        var unitGroupCount = unitDatas[i].unitGroupCount;
        for (var groupName in unitGroupCount) {
            unitCounts[groupName].push(unitGroupCount[groupName]);
        }
    }

    var optionRs = me.GetSubmitChartOption({
        itemCounts: unitCounts,
        itemNames: unitNames
    });

    res.data = {
        names: unitNames,
        counts: unitCounts,
        option: optionRs.data
    };
    res.success = true;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryTempList"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryTempList
 * @description   查询临时处理单清单列表 wanghq 2024年11月8日10:13:28
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate    
 * @param    {STRING}    endDate    
 * @param    {STRING}    childType    
 * @param    {INTEGER}    page        {"aspect.defaultValue":"1"}
 * @param    {INTEGER}    limit        {"aspect.defaultValue":"10"}
 * @param    {STRING}    status    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    // 判断是否需要分页
    var isAllData = limit === -1 || limit === 0;
    var startRowno = isAllData ? 1 : (page - 1) * limit + 1;
    var endRowno = isAllData ? 999999 : page * limit;
    var baseSql = Things['Thing.Fn.ElectricTest'].GetTempSql({
        query: {
            startDate: startDate,
            endDate: endDate,
            category: childType,
            treeId: treeId
        }
    }).data;
    
    // 根据状态添加筛选条件
    var statusFilter = "";
    if (status === "已完成") {
        statusFilter = " WHERE IS_FINISHED = 1";
    } else if (status === "未完成") {
        statusFilter = " WHERE (IS_FINISHED = 0 OR IS_FINISHED IS NULL)";
    }
    
    // 计算总数
    var countSql = "select count(*) as count from(" + baseSql + ")" + statusFilter;
    var count = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql }).rows[0]["COUNT"];
    
    // 构建查询SQL
    var querySql = "select a.*,b.NAME,b.HTML_DATA,b.SAVE_DATA,b.TABLE_HEADER,b.TABLE_STATUS, b.IS_ELECTRIC_TEST from (" + baseSql + ") a left join QUALITY_REPORT b on a.REPORT_ID = b.ID";
    
    // 添加状态过滤条件
    if (statusFilter !== "") {
        querySql = "select * from (" + querySql + ")" + statusFilter;
    }
    
    // 添加排序
    querySql += " order by OPT_DATE desc";
    
    // 查询数据
    var pageDatas;
    if (isAllData) {
        // 不分页，直接查询所有数据
        pageDatas = Things["Thing.DB.Oracle"].RunQuery({ 
            sql: querySql
        }).ToJSON().rows;
    } else {
        // 分页查询
        pageDatas = Things["Thing.DB.Oracle"].RunQuery({ 
            sql: "select * from (select s.* ,ROWNUM rowno from(" + querySql + ") s) where rowno &gt;= " + startRowno + " and rowno &lt;= " + endRowno 
        }).ToJSON().rows;
    }

    res = {
        code: 0,
        msg: "成功",
        count: count,
        data: pageDatas
    };
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryTemporaryCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryTemporaryCount
 * @description   查询现场临时处理单的数量 wanghq 2024年11月7日18:38:30
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var queryRes = Things['Thing.Fn.ElectricTest'].QueryTempStatisticsModel({
        query: {
            "treeId": treeId,
            "startDate": startDate,
            "endDate": endDate,
            "username": username
        }
    }).data;

    var allData = queryRes.data;
    var dataSize = queryRes.dataSize;
    var categorys = queryRes.categorys;
    //截断initData到下标dataSize
    var initData = allData.splice(0, dataSize);
    var totalData = allData[0];
    //降序排序
    initData.sort(function (x, y) {
        return y['合计'] - x['合计'];
    });
    if (initData.length &lt;= 10) {
        for (var i = 0; i &lt;= 10; i++) {
            if (initData.length &lt; i) {
                var empty = {
                    "MODEL_NAME": '',
                    "MODEL_ID": i
                };
                for (var i = 0; i &lt; categorys.length; i++) {
                    empty[categorys[i]] = '';
                }
                empty['合计'] = '';
                initData.push(empty);
            }
        }
    }


    categorys = categorys.splice(0, categorys.length - 1);
    var firstRow = ['名称', '全部'];
    for (var i = 0; i &lt; categorys.length; i++) {
        firstRow.push(categorys[i]);
    }
    var dataset = [firstRow];
    for (var i = 0; i &lt; initData.length; i++) {
        var row = [];
        row.push(initData[i]['MODEL_NAME'] + '~~~' + initData[i]['MODEL_ID']);
        row.push(initData[i]['合计']);
        for (var j = 0; j &lt; categorys.length; j++) {
            row.push(initData[i][categorys[j]]);
        }
        dataset.push(row);
    }
    var total = {};
    total['全部'] = totalData['合计'] || 0;
    for (var j = 0; j &lt; categorys.length; j++) {
        total[categorys[j]] = totalData[categorys[j]] || 0;
    }
    
    // 获取完成状态数据
    var completionStatusData = Things['Thing.Fn.ElectricTest'].QueryTempCompletionStatus({
        query: {
            "treeId": treeId,
            "startDate": startDate,
            "endDate": endDate,
            "username": username
        }
    }).data;
    
    var option = me.GetTempChartOption({ categorysLen: categorys.length });
    option.dataZoom[0].show = initData.length &gt; 10;
    option.dataset.source = dataset;
    res.success = true;
    res.data = {
        option: option,
        total: total,
        completionStatus: completionStatusData // 添加完成状态数据
    };
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="QueryUpdateTime"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    QueryUpdateTime
 * @description   查询更新时间 wanghq 2023年4月19日14:25:31
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var rs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select UPDATE_TIME from (select * from MODEL_STATISTICS where STAT_TYPE='现场问题处理单' order by UPDATE_TIME desc) where ROWNUM=1" });
    res.success = true;
    res.data = "更新时间：" + rs.rows[0]['UPDATE_TIME'];
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="SetModelPhaseCurrentAITNode"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    SetModelPhaseCurrentAITNode
 * @description   手动设置的当前阶段型号的AIT过程节点名称 datetime 2024年1月10日19:03:18
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId    
 * @param    {STRING}    aitNode    
 *
 * @returns    {JSON}
 */
var res = {};
try {

    Things['Thing.DB.Oracle'].RunCommand({ sql: "update DATAPACKAGETREE set CURRENT_AIT_NODE='" + aitNode + "' where TREEID=" + treeId });
    res.success = true;
    res.msg = "设置成功";
} catch (error) {
    res.success = false;
    var msg = "SetModelPhaseCurrentAITNode-手动设置的当前阶段型号的AIT过程节点名称失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="StrIsEmpty"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    StrIsEmpty
 * @description   判断字符串是否为空 datetime 2024年1月12日16:29:43
 * @implementation    {Script}
 *
 * @param    {STRING}    str    
 *
 * @returns    {BOOLEAN}
 */
function isEmpty(str) {
    return !(str != " " &amp;&amp; str != "" &amp;&amp; str != undefined &amp;&amp; str != 'undefined' &amp;&amp; str != null &amp;&amp; str != 'null');
}
result = isEmpty(str);</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="SyncMESModelProgress"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    SyncMESModelProgress
 * @description   同步MES型号进度 wanghq 2025年4月17日16:22:26
 * @implementation    {Script}
 *
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //获取项目清单接口路径
    var reqUrl = Things["Thing.Fn.SystemDic"].getKeyByNames({
        name: 'MES项目清单接口路径',
        pname: '系统配置'
    });

    var content =
        '&lt;?xml version="1.0" encoding="utf-8"?&gt;\
			    &lt;soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"&gt;\
			      &lt;soap:Body&gt;\
			        &lt;RetrieveDailyTaskByWPId xmlns="http://tempuri.org/"&gt;\
			        &lt;/RetrieveDailyTaskByWPId&gt;\
			      &lt;/soap:Body&gt;\
			    &lt;/soap:Envelope&gt;';

    //PostXML所需参数
    var params = {
        headers: {
            "Content-Type": "text/xml; charset=utf-8"
        },
        url: reqUrl,
        timeout: 6000000,
        content: content
    };
    var resultXml = Resources["ContentLoaderFunctions"].PostXML(params);
    //解析返回的xml
    var contentXml = resultXml.*:: Body.*:: RetrieveDailyTaskByWPIdResponse;

    resultXml = String(contentXml.*:: RetrieveDailyTaskByWPIdResult);
    resultXml = resultXml.substring(resultXml.indexOf("&lt;Response"), resultXml.indexOf("&lt;/Response&gt;") + 11);
    //结果集的xml
    var xml = new XML(resultXml);

    // 统计计数器
    var insertCount = 0;
    var skipCount = 0;
    var errorCount = 0;

    // 第一步：汇总所有model_code
    var modelCodeMap = {};
    for each(var tag in xml.Item) {
        try {
            var code = String(tag.Code);
            if (code &amp;&amp; code.trim() !== '') {
                modelCodeMap[code] = true;
            }
        } catch (itemError) {
            logger.warn("汇总model_code时发生错误，错误信息: " + itemError);
        }
    }

    // 获取所有唯一的model_code
    var modelCodes = [];
    for (var code in modelCodeMap) {
        modelCodes.push(code);
    }

    // 第二步：批量删除这些model_code对应的所有记录
    if (modelCodes.length &gt; 0) {
        var deleteSql = "DELETE FROM MES_MODEL_PROGRESS WHERE MODEL_CODE IN ('" + modelCodes.join("','") + "')";
        try {
            Things["Thing.DB.Oracle"].RunCommand({
                sql: deleteSql
            });
            logger.info("批量删除完成，共删除 " + modelCodes.length + " 个型号的数据");
        } catch (deleteError) {
            logger.warn("批量删除时发生错误: " + deleteError);
        }
    }

    // 第三步：处理结果集 - 依次插入item的数据
    for each(var tag in xml.Item) {
        try {
            var code = String(tag.Code);
            var stage = String(tag.Stage);
            var developState = String(tag.DevelopState);

            // 检查必要字段是否为空，如果为空则跳过
            if (!developState || developState.trim() === '' ||
                !code || code.trim() === '' ||
                !stage || stage.trim() === '') {
                logger.info("跳过空字段的记录，MODEL_CODE: " + code + ", STAGE: " + stage + ", DEVELOP_STATE: " + developState);
                continue;
            }

            // 检查是否已存在相同的记录（code + stage + developState）
            var checkSql = "SELECT COUNT(*) as CNT FROM MES_MODEL_PROGRESS WHERE MODEL_CODE = '" + code + "' AND CODE = '" + stage + "' AND DEVELOP_STAGE = '" + developState + "'";
            var checkResult = Things["Thing.DB.Oracle"].RunQuery({
                sql: checkSql
            });

            if (checkResult &amp;&amp; checkResult.rows &amp;&amp; checkResult.rows.length &gt; 0 &amp;&amp; checkResult.rows[0].CNT &gt; 0) {
                skipCount++;
                logger.info("跳过已存在的记录，MODEL_CODE: " + code + ", STAGE: " + stage + ", DEVELOP_STATE: " + developState);
                continue;
            }

            // 执行插入操作
            Things["Thing.DB.Oracle"].RunCommand({
                sql: "INSERT INTO MES_MODEL_PROGRESS(MODEL_CODE, CODE, DEVELOP_STAGE, SYNC_TIME) VALUES('" + code + "', '" + stage + "', '" + developState + "', SYSDATE)"
            });

            // 统计插入数量
            insertCount++;

        } catch (itemError) {
            errorCount++;
            logger.warn("处理单条数据时发生错误，MODEL_CODE: " + code + "，错误信息: " + itemError);
        }
    }

    res.success = true;
    res.data = {
        insertCount: insertCount,
        skipCount: skipCount,
        errorCount: errorCount,
        totalCount: insertCount + skipCount + errorCount
    };
    res.msg = "SyncMESModelProgress同步MES型号进度成功，共处理" + (insertCount + skipCount + errorCount) +
        "条数据（插入:" + insertCount + "条" + (skipCount &gt; 0 ? "，跳过:" + skipCount + "条" : "") + (errorCount &gt; 0 ? "，失败:" + errorCount + "条" : "") + "）";

    // 记录详细的同步结果
    logger.info(res.msg);

} catch (error) {
    res.success = false;
    res.msg = "SyncMESModelProgress同步MES型号进度失败，原因：" + error;
    logger.error(res.msg);
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateAllData"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateAllData
 * @description   wanghq 2025年5月21日14:15:0
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 *
 * @returns    {NOTHING}
 */
Things['Thing.Fn.ElectricTest'].InitTemporaryTable();
me.UpdateProblemCount({ treeId: treeId });
me.UpdateModelSubmitCount({ treeId: treeId });
me.UpdateModelTypeListCount({ treeId: treeId });
me.SyncMESModelProgress();
me.UpdateChangeOrderCount();
me.UpdateNonconformityCount();</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateChangeOrderCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateChangeOrderCount
 * @description   更新技术状态更改单的统计数据 wanghq 2025年5月22日10:30:00
 * @implementation    {Script}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 查询所有型号
    var modelSql = "select * from PHASE_MODEL";
    var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql });
    // 记录成功更新的型号数量
    var successCount = 0;

    // 遍历所有型号，更新统计数据
    for (var i = 0; i &lt; modelRs.rows.length; i++) {
        var model = modelRs.rows[i];
        var modelId = model.TREEID;
        var modelName = model.MODEL_NAME;
        var phaseCode = model.PHASE_CODE;
        
        // 1. 统计技术状态更改单总数量
        var orderCountSql = "SELECT COUNT(*) AS COUNT FROM BPM_TC_CHANGE_ORDER WHERE XH = '" + modelName + "' AND YZJD = '" + phaseCode + "'";
        var orderCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: orderCountSql });
        var orderCount = orderCountRs.rows[0].COUNT;
        // 更新技术状态更改单总数量
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "技术状态更改单",
            statCount: orderCount
        });
        // 2. 统计技术状态更改单分支总数量
        var branchCountSql = "SELECT COUNT(*) AS COUNT FROM BPM_TC_CHANGE_BRANCH b " +
            "JOIN BPM_TC_CHANGE_ORDER o ON b.BINDID = o.BINDID " +
            "WHERE o.XH = '" + modelName + "' AND o.YZJD = '" + phaseCode + "'";
        var branchCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: branchCountSql });
        var branchCount = branchCountRs.rows[0].COUNT;
        // 更新技术状态更改单分支总数量
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "技术状态更改单_分支",
            statCount: branchCount
        });
        // 3. 统计已完成和未完成的数量
        var statusCountSql = "SELECT ISEND, COUNT(*) AS COUNT FROM BPM_TC_CHANGE_ORDER " +
            "WHERE XH = '" + modelName + "' AND YZJD = '" + phaseCode + "' GROUP BY ISEND";
        var statusCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: statusCountSql });

        var finishedCount = 0;
        var unfinishedCount = 0;

        for (var j = 0; j &lt; statusCountRs.rows.length; j++) {
            var statusRow = statusCountRs.rows[j];
            if (statusRow.ISEND == 1) {  // 1表示已完成
                finishedCount = statusRow.COUNT;
            } else if (statusRow.ISEND == 0) {  // 0表示未完成
                unfinishedCount = statusRow.COUNT;
            }
        }
        
        // 更新已完成和未完成的数量
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "技术状态更改单~已完成",
            statCount: finishedCount
        });
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "技术状态更改单~未完成",
            statCount: unfinishedCount
        });
        // 3.1 统计分支已完成和未完成的数量
        var branchStatusCountSql = "SELECT o.ISEND, COUNT(b.FZ) AS COUNT " +
            "FROM BPM_TC_CHANGE_BRANCH b " +
            "JOIN BPM_TC_CHANGE_ORDER o ON b.BINDID = o.BINDID " +
            "WHERE o.XH = '" + modelName + "' AND o.YZJD = '" + phaseCode + "' " +
            "GROUP BY o.ISEND";
        var branchStatusCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: branchStatusCountSql });

        var branchFinishedCount = 0;
        var branchUnfinishedCount = 0;

        for (var j = 0; j &lt; branchStatusCountRs.rows.length; j++) {
            var branchStatusRow = branchStatusCountRs.rows[j];
            if (branchStatusRow.ISEND == 1) {  // 1表示已完成
                branchFinishedCount = branchStatusRow.COUNT;
            } else if (branchStatusRow.ISEND == 0) {  // 0表示未完成
                branchUnfinishedCount = branchStatusRow.COUNT;
            }
        }
        
        // 更新分支已完成和未完成的数量
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "技术状态更改单_分支~已完成",
            statCount: branchFinishedCount
        });
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "技术状态更改单_分支~未完成",
            statCount: branchUnfinishedCount
        });
        // 4. 统计各种情况的数量
        // 查询所有情况类型
        var situationTypesSql = "SELECT DISTINCT JTQK FROM BPM_TC_CHANGE_BRANCH WHERE JTQK IS NOT NULL ORDER BY JTQK";
        var situationTypesRs = Things["Thing.DB.Oracle"].RunQuery({ sql: situationTypesSql });

        // 构建情况类型数组
        var situationTypes = [];
        for (var k = 0; k &lt; situationTypesRs.rows.length; k++) {
            situationTypes.push(situationTypesRs.rows[k].JTQK);
        }
        // 查询各种情况的总数
        var situationCountSql = "SELECT b.JTQK, COUNT(*) AS COUNT FROM BPM_TC_CHANGE_BRANCH b " +
            "JOIN BPM_TC_CHANGE_ORDER o ON b.BINDID = o.BINDID " +
            "WHERE o.XH = '" + modelName + "' AND o.YZJD = '" + phaseCode + "' GROUP BY b.JTQK";
        var situationCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: situationCountSql });

        // 初始化各种情况的计数
        var situationCounts = {};
        for (var k = 0; k &lt; situationTypes.length; k++) {
            situationCounts[situationTypes[k]] = 0;
        }

        // 统计各种情况的数量
        for (var m = 0; m &lt; situationCountRs.rows.length; m++) {
            var situationRow = situationCountRs.rows[m];
            var situation = situationRow.JTQK;
            var count = situationRow.COUNT;

            if (situationCounts.hasOwnProperty(situation)) {
                situationCounts[situation] = count;
            }
        }

        // 更新各种情况的数量
        for (var situation in situationCounts) {
            me.UpdateModelStatistics({
                modelId: modelId,
                statType: "技术状态更改单_" + situation,
                statCount: situationCounts[situation]
            });
        }

        // 查询各种情况的完成/未完成状态数量
        var situationStatusSql = "SELECT b.JTQK, o.ISEND, COUNT(*) AS COUNT FROM BPM_TC_CHANGE_BRANCH b " +
            "JOIN BPM_TC_CHANGE_ORDER o ON b.BINDID = o.BINDID " +
            "WHERE o.XH = '" + modelName + "' AND o.YZJD = '" + phaseCode + "' GROUP BY b.JTQK, o.ISEND";
        var situationStatusRs = Things["Thing.DB.Oracle"].RunQuery({ sql: situationStatusSql });
        // 初始化各种情况的完成/未完成计数
        var situationStatusCounts = {};
        for (var k = 0; k &lt; situationTypes.length; k++) {
            var situationType = situationTypes[k];
            situationStatusCounts[situationType] = {
                finished: 0,
                unfinished: 0
            };
        }

        // 统计各种情况的完成/未完成数量
        for (var m = 0; m &lt; situationStatusRs.rows.length; m++) {
            var statusRow = situationStatusRs.rows[m];
            var situation = statusRow.JTQK;
            var status = statusRow.ISEND;
            var count = statusRow.COUNT;

            if (situationStatusCounts.hasOwnProperty(situation)) {
                if (statusRow.ISEND == 1) {  // 1表示已完成
                    situationStatusCounts[situation].finished = count;
                } else if (statusRow.ISEND == 0) {  // 0表示未完成
                    situationStatusCounts[situation].unfinished = count;
                }
            }
        }
        // 更新各种情况的完成/未完成数量
        for (var situation in situationStatusCounts) {
            var statusData = situationStatusCounts[situation];

            // 更新已完成数量
            me.UpdateModelStatistics({
                modelId: modelId,
                statType: "技术状态更改单_" + situation + "~已完成",
                statCount: statusData.finished
            });
            // 更新未完成数量
            me.UpdateModelStatistics({
                modelId: modelId,
                statType: "技术状态更改单_" + situation + "~未完成",
                statCount: statusData.unfinished
            });
        }

        successCount++;
    }

    res.success = true;
    res.msg = "更新技术状态更改单统计数据成功，共更新" + successCount + "个型号的数据";
} catch (error) {
    res.success = false;
    res.msg = "更新技术状态更改单统计数据失败，原因：" + error;
    logger.error("[UpdateChangeOrderCount] " + res.msg);
}
result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateModelStatistics"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateModelStatistics
 * @description   更新型号的相关数据统计信息 wanghq 2023年4月13日14:11:19
 * @implementation    {Script}
 *
 * @param    {NUMBER}    modelId    
 * @param    {STRING}    statType    
 * @param    {NUMBER}    statCount    
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var nowTime = dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss");
    var querySql = "select id from MODEL_STATISTICS where model_id=" + modelId + " and stat_type='" + statType + "'";
    var rs = Things["Thing.DB.Oracle"].RunQuery({ sql: querySql });
    if (rs.rows.length &gt; 0) {
        //更新
        var updateSql = "update MODEL_STATISTICS set STAT_COUNT=" + statCount + ",update_time='" + nowTime + "' where id=" + rs.rows[0]["ID"];
        Things["Thing.DB.Oracle"].RunCommand({ sql: updateSql });
    } else {
        //新增
        var insertSql = "insert into MODEL_STATISTICS (ID, MODEL_ID, STAT_TYPE, STAT_COUNT, UPDATE_TIME) values (MODEL_STATISTICS_SEQ.nextval," + modelId + ",'" + statType + "'," + statCount + ",'" + nowTime + "')";
        Things["Thing.DB.Oracle"].RunCommand({ sql: insertSql });
    }
    res.success = true;
    res.msg = "更新型号的相关数据统计信息成功";
} catch (error) {
    res.success = false;
    res.msg = "更新型号的相关数据统计信息失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateModelSubmitCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateModelSubmitCount
 * @description   更新型号的产品交接单数量 wanghq 2023年4月13日15:48:56
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var groupTypes = ["ISCERTIFICATE", "ISLUOHAN", "ISSUBMIT"];

    var nullText = "未知";
    var fileType = "产品交接单";

    //获取所有的型号
    var modelSql = "select * from phase_model";
    if (treeId != -1) {
        modelSql += " where treeid=" + treeId;
    }
    var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql });
    for (var j = 0; j &lt; groupTypes.length; j++) {
        var groupType = groupTypes[j];
        var statParentType = "产品交接单_" + groupType + "_";

        var groupNames = [];
        var groupRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select distinct nvl(" + groupType + ",'" + nullText + "') as GROUP_NAME from V_LATEST_PRODUCT_SUBMIT" });
        for (var i = 0; i &lt; groupRs.rows.length; i++) {
            var groupName = groupRs.rows[i]['GROUP_NAME'];
            groupNames.push(groupName);
        }

        for (var i = 0; i &lt; modelRs.rows.length; i++) {
            var model = modelRs.rows[i];
            var modelId = model.TREEID;
            var submitListSql = me.GetSubmitListSql({
                treeId: modelId
            });
            var totalGroupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;
            var totalGroupCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalGroupCountSql });

            for (var x = 0; x &lt; groupNames.length; x++) {
                var allGroupName = groupNames[x];
                var allGroupCount = 0;
                for (var m = 0; m &lt; totalGroupCountRs.rows.length; m++) {
                    var group = totalGroupCountRs.rows[m];
                    var groupName = group["GROUP_NAME"];
                    if (allGroupName == groupName) {
                        allGroupCount = group["COUNT"];
                        break;
                    }
                }
                var statType = statParentType + allGroupName;
                me.UpdateModelStatistics({
                    modelId: modelId,
                    statType: statType,
                    statCount: allGroupCount
                });
            }

        }
    }

    res.success = true;
    res.msg = "更新型号的清单数量成功";
} catch (error) {
    res.success = false;
    res.msg = "更新型号的清单数量失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateModelTypeListCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateModelTypeListCount
 * @description   更新型号的清单数量 wanghq 2023年4月13日14:28:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 *
 * @returns    {JSON}
 */
var res = {};
try {

    function updateTechStateCount(modelId, state) {
        var whereSql = me.GetWhereSql({
            treeId: modelId,
            fileType: '现场问题处理单'
        });

        var querySql = "select * " +
            "from (select * from RESULTGATHER " + whereSql + ") m " +
            "         left join XMLDATA_TECHPROBLEM n on n.SOURCE_ID like '%' || m.TABLENAME || '%' and n.RESULT_ID = m.ID ";
        if (state == '已闭环') {
            querySql += " where n.STATE='7'";
        } else if (state == '未闭环') {
            querySql += " where n.STATE!='7'";
        }
        var countSql = "select count(*) as COUNT  from (" + querySql + ")";
        var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql });
        var statCount = countRs.rows[0].COUNT;
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "现场问题处理单~" + state,
            statCount: statCount
        });
    }

    var fileTyles = ["现场问题处理单", "工艺更改单", "设计更改单", "工艺偏离单", "设计偏离单", "影像记录"];

    //获取所有的型号
    var modelSql = "select * from phase_model";
    if (treeId != -1) {
        modelSql += " where treeid=" + treeId;
    }
    var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql });
    for (var i = 0; i &lt; modelRs.rows.length; i++) {
        var model = modelRs.rows[i];
        var modelId = model.TREEID;
        for (var j = 0; j &lt; fileTyles.length; j++) {
            var statType = fileTyles[j];
            var countSql = me.GetQueryCountSql({
                treeId: modelId,
                fileType: statType
            });
            var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql });
            var statCount = countRs.rows[0].COUNT;
            me.UpdateModelStatistics({
                modelId: modelId,
                statType: statType,
                statCount: statCount
            });

            if (statType == "现场问题处理单") {
                updateTechStateCount(modelId, "已闭环");
                updateTechStateCount(modelId, "未闭环");
            }
        }
    }

    res.success = true;
    res.msg = "更新型号的类型清单数量成功";
} catch (error) {
    res.success = false;
    res.msg = "更新型号的类型清单数量失败，原因：" + error;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateNonconformityCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateNonconformityCount
 * @description   更新不合格品审理单的统计数据 wanghq 2025年5月23日
 * @implementation    {Script}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    // 查询所有型号
    var modelSql = "select * from PHASE_MODEL";
    var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql });

    // 记录成功更新的型号数量
    var successCount = 0;

    // 遍历所有型号，更新统计数据
    for (var i = 0; i &lt; modelRs.rows.length; i++) {
        var model = modelRs.rows[i];
        var modelId = model.TREEID;
        var modelName = model.MODEL_NAME;
        var phaseCode = model.PHASE_CODE;   

        // 1. 统计不合格品审理单总数量
        var countSql = "SELECT COUNT(*) AS COUNT FROM BPM_NONCONFORMITY_REVIEW WHERE XH = '" + modelName + "' AND YZJD = '" + phaseCode + "'";
        var countRs = Things["Thing.DB.Oracle"].RunQuery({ sql: countSql });
        var totalCount = countRs.rows[0].COUNT;

        // 更新不合格品审理单总数量
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "不合格品审理单",
            statCount: totalCount
        });

        // 2. 统计已完成和未完成的数量
        var statusCountSql = "SELECT ISEND, COUNT(*) AS COUNT FROM BPM_NONCONFORMITY_REVIEW " +
            "WHERE XH = '" + modelName + "' AND YZJD = '" + phaseCode + "' GROUP BY ISEND";
        var statusCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: statusCountSql });

        var finishedCount = 0;
        var unfinishedCount = 0;

        for (var j = 0; j &lt; statusCountRs.rows.length; j++) {
            var statusRow = statusCountRs.rows[j];
            if (statusRow.ISEND === "1") {
                finishedCount = statusRow.COUNT;
            } else if (statusRow.ISEND === "0") {
                unfinishedCount = statusRow.COUNT;
            }
        }

        // 更新已完成和未完成的数量
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "不合格品审理单~已完成",
            statCount: finishedCount
        });

        me.UpdateModelStatistics({
            modelId: modelId,
            statType: "不合格品审理单~未完成",
            statCount: unfinishedCount
        });

        // 获取固定的严重程度配置
        var allSeverityLevels = me.GetSeverityLevels().data;

        // 初始化各严重程度的计数
        var severityCounts = {};
        for (var j = 0; j &lt; allSeverityLevels.length; j++) {
            var severityLevel = allSeverityLevels[j];
            severityCounts[severityLevel] = 0;
        }

        // 3. 统计各严重程度的数量
        var severityCountSql = "SELECT BHGPYZCD, COUNT(*) AS COUNT FROM BPM_NONCONFORMITY_REVIEW " +
            "WHERE XH = '" + modelName + "' AND YZJD = '" + phaseCode + "' GROUP BY BHGPYZCD";
        var severityCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: severityCountSql });

        // 统计各严重程度的数量
        for (var k = 0; k &lt; severityCountRs.rows.length; k++) {
            var severityRow = severityCountRs.rows[k];
            var severity = severityRow.BHGPYZCD;
            var count = severityRow.COUNT;

            if (severityCounts.hasOwnProperty(severity)) {
                severityCounts[severity] = count;
            }
        }

        // 更新各严重程度的数量
        for (var severity in severityCounts) {
            me.UpdateModelStatistics({
                modelId: modelId,
                statType: "不合格品审理单_" + severity,
                statCount: severityCounts[severity]
            });
        }

        // 4. 统计各严重程度的完成/未完成状态数量
        var severityStatusSql = "SELECT BHGPYZCD, ISEND, COUNT(*) AS COUNT FROM BPM_NONCONFORMITY_REVIEW " +
            "WHERE XH = '" + modelName + "' AND YZJD = '" + phaseCode + "' GROUP BY BHGPYZCD, ISEND";
        var severityStatusRs = Things["Thing.DB.Oracle"].RunQuery({ sql: severityStatusSql });

        // 初始化各严重程度的完成/未完成计数
        var severityStatusCounts = {};
        for (var j = 0; j &lt; allSeverityLevels.length; j++) {
            var severityLevel = allSeverityLevels[j];
            severityStatusCounts[severityLevel] = { finished: 0, unfinished: 0 };
        }

        // 统计各严重程度的完成/未完成数量
        for (var m = 0; m &lt; severityStatusRs.rows.length; m++) {
            var statusRow = severityStatusRs.rows[m];
            var severity = statusRow.BHGPYZCD;
            var statusValue = statusRow.ISEND;
            var count = statusRow.COUNT;

            if (severityStatusCounts.hasOwnProperty(severity)) {
                if (statusValue === "1") {
                    severityStatusCounts[severity].finished = count;
                } else if (statusValue === "0") {
                    severityStatusCounts[severity].unfinished = count;
                }
            }
        }

        // 更新各严重程度的完成/未完成数量
        for (var severity in severityStatusCounts) {
            var statusData = severityStatusCounts[severity];

            // 更新已完成数量
            me.UpdateModelStatistics({
                modelId: modelId,
                statType: "不合格品审理单_" + severity + "~已完成",
                statCount: statusData.finished
            });

            // 更新未完成数量
            me.UpdateModelStatistics({
                modelId: modelId,
                statType: "不合格品审理单_" + severity + "~未完成",
                statCount: statusData.unfinished
            });
        }

        successCount++;
    }

    res.success = true;
    res.msg = "更新不合格品审理单统计数据成功，共更新" + successCount + "个型号的数据";
} catch (error) {
    res.success = false;
    res.msg = "更新不合格品审理单统计数据失败，原因：" + error;
}
result = res;
</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation><ServiceImplementation description="" handlerName="Script" name="UpdateProblemCount"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>/**
 * @definition    UpdateProblemCount
 * @description   更新现场问题处理单中的更改设计文件和更改工艺文件 datetime 2024年1月12日14:16:16
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    //获取所有的型号
    var modelSql = "select * from phase_model";
    if (treeId != -1) {
        modelSql += " where treeid=" + treeId;
    }
    var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql });

    function updateCount(modelId, statType, state) {
        var countSql = me.GetProblemTypeCountSql({
            treeId: modelId,
            childType: statType,
            state: state
        });
        var statCount = Things['Thing.DB.Oracle'].RunQuery({ sql: countSql }).rows[0]['COUNT'];
        var statType = "现场问题处理单_" + statType;
        if (state) {
            statType += "~" + state;
        }
        me.UpdateModelStatistics({
            modelId: modelId,
            statType: statType,
            statCount: statCount
        });

    }

    for (var i = 0; i &lt; modelRs.rows.length; i++) {
        var model = modelRs.rows[i];
        var modelId = model.TREEID;
        updateCount(modelId, "更改工艺文件", "");
        updateCount(modelId, "更改设计文件", "");
        updateCount(modelId, "更改工艺文件", "已完成");
        updateCount(modelId, "更改工艺文件", "未完成");
        updateCount(modelId, "更改设计文件", "已完成");
        updateCount(modelId, "更改设计文件", "未完成");
    }
    res.success = true;
    res.msg = "更新现场问题处理单中的成功";
} catch (error) {
    res.success = false;
    var msg = "UpdateProblemCount-更新现场问题处理单中的失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}
result = res;</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation></ServiceImplementations><Subscriptions><Subscription enabled="true" eventName="ScheduledEvent" source="Thing.Timer.BigScreen" sourceProperty="" sourceType="Thing"><ServiceImplementation description="" handlerName="Script" name="Type.Thing:Entity.Thing.Timer.BigScreen:Event.ScheduledEvent"><ConfigurationTables><ConfigurationTable description="" isMultiRow="false" name="Script" ordinal="0"><DataShape><FieldDefinitions><FieldDefinition baseType="STRING" description="code" name="code" ordinal="0"/></FieldDefinitions></DataShape><Rows><Row><code>me.UpdateAllData();</code></Row></Rows></ConfigurationTable></ConfigurationTables></ServiceImplementation></Subscription></Subscriptions></ThingShape><PropertyBindings/><RemotePropertyBindings/><RemoteServiceBindings/><RemoteEventBindings/><AlertConfigurations><AlertDefinitions name="listBarWidth"/><AlertDefinitions name="problemBarWidth"/><AlertDefinitions name="submitBarWidth"/></AlertConfigurations><ImplementedShapes/><ThingProperties><problemBarWidth><Value>25</Value><Timestamp>2024-01-23T14:43:18.735+08:00</Timestamp><Quality>GOOD</Quality></problemBarWidth><listBarWidth><Value>30</Value><Timestamp>2024-01-23T18:09:43.195+08:00</Timestamp><Quality>GOOD</Quality></listBarWidth><submitBarWidth><Value>30</Value><Timestamp>2024-01-23T18:09:16.465+08:00</Timestamp><Quality>GOOD</Quality></submitBarWidth></ThingProperties></Thing></Things></Entities>