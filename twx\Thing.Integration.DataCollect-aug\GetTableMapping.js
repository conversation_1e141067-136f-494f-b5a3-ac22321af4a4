/**
 * @definition    GetTableMapping    {"category":"ext"}
 * @description   获取表名映射关系  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    category    数据类别（设计、工艺、过程控制、质量综合）
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!category || category === "") {
        throw "数据类别不能为空";
    }
    
    var tableType = "";
    var isValid = true;
    
    // 根据类别映射表名
    if (category === "设计") {
        tableType = "DESIGN_DATA_RESULT";
    } else if (category === "工艺") {
        tableType = "CRAFT_DATA_RESULT";
    } else if (category === "质量综合") {
        tableType = "QUALITY_CONTROL_RESULT";
    } else if (category === "过程控制") {
        tableType = "PROCESS_CONTROL_RESULT";
    } else {
        isValid = false;
        tableType = "";
    }
    
    if (!isValid) {
        res.success = false;
        res.msg = "不支持的数据类别：" + category;
    } else {
        res.success = true;
        res.msg = "获取表名映射成功";
    }
    
    res.data = {
        category: category,
        tableType: tableType,
        isValid: isValid
    };
    
} catch (error) {
    res.success = false;
    var msg = "GetTableMapping-获取表名映射失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
