/**
 * @definition    UpdateStatusBatch    {"category":"ext"}
 * @description   批量更新数据状态为old  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID    {"aspect.defaultValue":"0"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 定义需要更新的表名数组
    var tables = [
        'DESIGN_DATA_RESULT',
        'CRAFT_DATA_RESULT',
        'PROCESS_CONTROL_RESULT',
        'QUALITY_CONTROL_RESULT'
    ];
    
    var updateResults = [];
    var totalUpdated = 0;
    
    // 批量构建和执行更新语句
    for (var i = 0; i < tables.length; i++) {
        var table = tables[i];
        var sql = "";
        
        try {
            if (treeid !== 0) {
                // 更新指定节点的数据
                sql = "update " + table + " set STATUS='old' " +
                      "where NODECODE in (select id from DATA_PACKAGE where REFTREEID=" + treeid + ") " +
                      "and STATUS != 'old'";
            } else {
                // 更新所有数据
                sql = "update " + table + " set STATUS='old' where STATUS != 'old'";
            }
            
            // 执行更新
            var updateResult = Things['Thing.DB.Oracle'].RunCommand({
                sql: sql
            });
            
            updateResults.push({
                table: table,
                success: true,
                sql: sql,
                result: updateResult
            });
            
            // 记录更新信息
            logger.info("UpdateStatusBatch-表更新成功：" + table + "，treeid=" + treeid);
            
        } catch (tableError) {
            updateResults.push({
                table: table,
                success: false,
                sql: sql,
                error: String(tableError)
            });
            
            logger.error("UpdateStatusBatch-表更新失败：" + table + "，错误：" + tableError);
        }
    }
    
    // 统计成功更新的表数量
    var successCount = 0;
    for (var j = 0; j < updateResults.length; j++) {
        if (updateResults[j].success) {
            successCount++;
        }
    }
    
    res.success = successCount > 0;
    res.data = {
        totalTables: tables.length,
        successCount: successCount,
        failureCount: tables.length - successCount,
        updateResults: updateResults
    };
    
    if (successCount === tables.length) {
        res.msg = "批量更新状态完成，成功更新" + successCount + "个表";
    } else if (successCount > 0) {
        res.msg = "批量更新状态部分成功，成功更新" + successCount + "个表，失败" + (tables.length - successCount) + "个表";
    } else {
        res.msg = "批量更新状态失败，所有表更新都失败";
    }
    
    // 记录操作信息
    logger.info("UpdateStatusBatch-批量更新完成，treeid=" + treeid + "，成功=" + successCount + "，总计=" + tables.length);
    
} catch (error) {
    res.success = false;
    var msg = "UpdateStatusBatch-批量更新状态失败，原因：" + error;
    logger.error(msg + "，treeid=" + treeid);
    res.msg = msg;
}

result = res;
