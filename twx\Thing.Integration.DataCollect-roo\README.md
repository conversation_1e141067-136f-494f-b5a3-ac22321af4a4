# Thing.Integration.DataCollect-roo 重构项目

## 项目概述

这是对 `Thing.Integration.DataCollect/automaticCollect.js` 服务的重构版本，使用模块化架构重新设计了自动采集功能。

## 重构目标

- **代码结构优化**：将单一大型函数拆分为多个专用服务
- **安全性提升**：添加SQL注入防护和输入验证
- **错误处理完善**：实现标准JSON响应结构和详细错误日志
- **性能优化**：减少循环中的I/O操作，实现批量处理
- **代码规范统一**：严格按照Thingworx开发规范编写

## 服务架构

### 1. DatabaseService.js
- **功能**：提供安全的数据库操作接口
- **特性**：
  - SQL注入防护
  - 统一的错误处理
  - 支持参数化查询
  - 详细的操作日志

### 2. DataQueryService.js
- **功能**：查询需要自动采集的数据列表
- **特性**：
  - 支持按treeid过滤
  - 统一的查询条件处理
  - 结果数量统计

### 3. SoapRequestService.js
- **功能**：构建和发送SOAP请求到MES系统
- **特性**：
  - XML请求构建
  - 响应解析和验证
  - 超时控制
  - 错误处理

### 4. FileProcessService.js
- **功能**：处理不同类型的文件下载和格式化
- **特性**：
  - 支持CheckCard（PDF+Excel）
  - 支持图片文件（Photo）
  - 支持普通文件类型
  - 文件格式验证

### 5. StructuralAssemblyService.js
- **功能**：处理特殊的结构装配文件解析
- **特性**：
  - 条件检查（是否为结构装配节点）
  - 文件名关键词匹配
  - 特殊业务逻辑处理

### 1. ConfigService.js
- **功能**：配置服务，提供系统配置相关功能
- **特性**：
  - 获取系统配置项
  - 配置验证
  - 错误处理

### 2. DatabaseService.js
- **功能**：提供安全的数据库操作接口
- **特性**：
  - SQL注入防护
  - 统一的错误处理
  - 支持参数化查询
  - 详细的操作日志

### 3. DataQueryService.js
- **功能**：查询需要自动采集的数据列表
- **特性**：
  - 支持按treeid过滤
  - 统一的查询条件处理
  - 结果数量统计

### 4. SoapRequestService.js
- **功能**：构建和发送SOAP请求到MES系统
- **特性**：
  - XML请求构建
  - 响应解析和验证
  - 超时控制
  - 错误处理

### 5. FileProcessService.js
- **功能**：处理不同类型的文件下载和格式化
- **特性**：
  - 支持CheckCard（PDF+Excel）
  - 支持图片文件（Photo）
  - 支持普通文件类型
  - 文件格式验证

### 6. DataSaveService.js
- **功能**：数据保存服务，负责将数据保存到结果表
- **特性**：
  - 支持多种文件类型
  - 处理新增和更新操作
  - 错误处理和日志记录

### 7. RecordProcessService.js
- **功能**：记录处理服务，负责处理单个数据记录
- **特性**：
  - 文件类型编码获取
  - SOAP请求处理
  - 记录数据处理
  - 错误处理

### 8. StructuralAssemblyService.js
- **功能**：结构装配文件解析服务
- **特性**：
  - 条件检查（是否为结构装配节点）
  - 文件名关键词匹配
  - 特殊业务逻辑处理

### 9. UtilService.js
- **功能**：工具服务，提供通用的工具函数
- **特性**：
  - 时间格式化
  - 状态更新
  - 表类型获取

### 10. AutomaticCollect.js
- **功能**：主服务函数，协调所有子服务
- **特性**：
  - 只保留主要执行流程
  - 调用各个专用服务
  - 标准JSON响应结构
  - 详细的处理统计
  - 完善的错误处理
  - 性能监控

## 技术改进

### 安全性改进
1. **SQL注入防护**：
   ```javascript
   // 安全转义函数
   function sqlEscape(value) {
       return String(value).replace(/'/g, "''");
   }
   ```

2. **输入验证**：
   ```javascript
   if (entype === "") {
       throw "文件类型编码不能为空";
   }
   ```

### 性能优化
1. **批量处理**：减少数据库查询次数
2. **错误隔离**：单个记录失败不影响整体流程
3. **资源复用**：重复使用数据库连接和网络连接

### 代码质量提升
1. **模块化设计**：单一职责原则
2. **错误处理统一**：标准JSON响应格式
3. **日志规范化**：详细的操作日志记录
4. **注释完善**：符合JSDoc规范

## 重构前后对比

### 重构前
- **代码行数**：450+ 行
- **函数复杂度**：单一大型函数
- **错误处理**：简单try-catch
- **安全性**：存在SQL注入风险
- **性能**：循环中多次I/O操作

### 重构后
- **代码总量**：9个专用服务文件
- **主服务**：只保留执行流程，调用子服务
- **每个服务**：职责单一，易于维护
- **错误处理**：完善的错误处理和日志
- **安全性**：SQL注入防护，输入验证
- **性能**：批量处理，减少I/O操作

## 使用方法

### 调用主服务
```javascript
var result = Things["Thing.Integration.DataCollect-roo"].AutomaticCollect({
    treeid: 0  // 0表示处理所有节点
});
```

### 响应格式
```javascript
{
    "success": true,
    "msg": "自动采集完成，成功：10，失败：0，耗时：5分30秒",
    "data": {
        "totalRecords": 10,
        "successCount": 10,
        "errorCount": 0,
        "processingTime": 330000,
        "details": [...]
    }
}
```

## 部署说明

1. 将所有文件部署到 `twx/Thing.Integration.DataCollect-roo/` 目录
2. 确保原有的辅助服务（如 `downloadFileTest.js`、`addDataToResultTable.js` 等）仍然可用
3. 更新调用方代码，指向新的服务路径

## 作者信息

- **作者**：wanghq
- **创建时间**：2025年8月21日19:33:30
- **版本**：1.0.0
- **备注**：严格按照Thingworx开发规范重构，实现了模块化、安全性、性能的全面提升

## 维护建议

1. **定期监控**：关注日志中的错误信息
2. **性能调优**：根据实际运行情况调整批量处理大小
3. **功能扩展**：新增文件类型时，扩展FileProcessService.js
4. **安全更新**：定期review SQL注入防护措施