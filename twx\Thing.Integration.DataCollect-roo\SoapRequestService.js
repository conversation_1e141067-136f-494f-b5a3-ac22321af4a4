/**
 * @definition    SoapRequestService    {"category":"ext"}
 * @description   SOAP请求处理服务，负责构建和发送SOAP请求到MES系统  wanghq 2025年8月21日19:31:20
 * @implementation    {Script}
 *
 * @param    {STRING}    reqUrl    请求URL
 * @param    {STRING}    processCode    过程节点编码
 * @param    {STRING}    category    数据类别
 * @param    {STRING}    entype    文件类型编码
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: null
};

try {
    var reqUrl = reqUrl || "";
    var processCode = processCode || "";
    var category = category || "";
    var entype = entype || "";

    if (reqUrl === "") {
        throw "请求URL不能为空";
    }
    if (processCode === "") {
        throw "过程节点编码不能为空";
    }
    if (category === "") {
        throw "数据类别不能为空";
    }
    if (entype === "") {
        throw "文件类型编码不能为空";
    }

    // 构建SOAP请求XML
    var soapContent = '<?xml version="1.0" encoding="utf-8"?>' +
        '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">' +
        '<soap:Body>' +
        '<GetProcessListInfo xmlns="http://tempuri.org/">' +
        '<xmlSend><Request><ProcessCode>' + processCode + '</ProcessCode><Category>' + category + '</Category><Type>' + entype + '</Type></Request></xmlSend>' +
        '</GetProcessListInfo>' +
        '</soap:Body>' +
        '</soap:Envelope>';

    logger.info("SoapRequestService-发送SOAP请求，ProcessCode：" + processCode + "，Category：" + category + "，Type：" + entype);

    // 发送POST请求
    var params = {
        headers: {
            "Content-Type": "text/xml; charset=utf-8"
        },
        url: reqUrl,
        timeout: 600000,
        content: soapContent
    };

    var response = Resources["ContentLoaderFunctions"].PostXML(params);

    if (response === null || response === "") {
        throw "SOAP请求返回空响应";
    }

    // 解析XML响应
    var contentXml = response.*::Body.*::GetProcessListInfoResponse;

    if (contentXml.length() === 0) {
        throw "SOAP响应格式错误，未找到GetProcessListInfoResponse节点";
    }

    var resultXml = String(contentXml.*::GetProcessListInfoResult);

    if (resultXml === null || resultXml === "") {
        throw "SOAP响应结果为空";
    }

    // 提取Response部分
    var responseStart = resultXml.indexOf("<Response");
    var responseEnd = resultXml.indexOf("</Response>") + 11;

    if (responseStart === -1 || responseEnd === -1) {
        throw "SOAP响应中未找到Response节点";
    }

    var xmlContent = resultXml.substring(responseStart, responseEnd);

    // 解析为XML对象
    var xml = new XML(xmlContent);

    result.data = xml;
    result.success = true;
    result.msg = "SOAP请求成功";
    logger.info("SoapRequestService-SOAP请求成功，获取到 " + xml.Record.length() + " 条记录");

} catch (error) {
    result.success = false;
    var msg = "SoapRequestService-SOAP请求失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;