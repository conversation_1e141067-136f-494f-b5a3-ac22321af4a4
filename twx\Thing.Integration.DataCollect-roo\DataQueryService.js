/**
 * @definition    DataQueryService    {"category":"ext"}
 * @description   数据查询服务，负责查询需要自动采集的数据列表  wanghq 2025年8月21日19:31:05
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID，0表示查询所有
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: []
};

try {
    var treeid = treeid || 0;

    // 构建查询SQL
    var sql = "SELECT a.*, b.REFTREEID FROM alldatalistview a, DATA_PACKAGE b WHERE a.NODECODE = b.ID AND a.gathering_method = '自动采集' AND a.collectstatus = '1'";

    if (treeid !== 0) {
        sql += " AND b.REFTREEID = " + treeid;
    }

    sql += " ORDER BY a.NODECODE";

    logger.info("DataQueryService-查询待采集数据，treeid：" + treeid);

    // 执行查询
    var queryResult = Things['Thing.DB.Oracle'].RunQuery({
        sql: sql
    });

    if (queryResult.rows.length > 0) {
        result.data = queryResult.rows;
        result.success = true;
        result.msg = "查询成功，找到 " + queryResult.rows.length + " 条待采集数据";
        logger.info("DataQueryService-查询成功，找到 " + queryResult.rows.length + " 条数据");
    } else {
        result.success = true;
        result.msg = "查询成功，无待采集数据";
        logger.info("DataQueryService-查询成功，无待采集数据");
    }

} catch (error) {
    result.success = false;
    var msg = "DataQueryService-查询待采集数据失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;