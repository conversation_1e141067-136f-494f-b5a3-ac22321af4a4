/**
 * @definition    QuerySubmitCount
 * @description   查询产品交接单的数量 wanghq 2023年4月2日00:02:30
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 * @param    {STRING}    groupType        {"aspect.defaultValue":"ISCERTIFICATE"}
 *
 * @returns    {JSON}
 */
var res = {};
try {


    /**
    * 获取数组的下标
    * @param arr
    * @param val
    * @returns {number}
    */
    function indexOf(arr, val) {
        for (var i = 0; i < arr.length; i++) {
            if (arr[i] == val) {
                return i;
            }
        }
        return -1;
    }
    /**
     * 判断一个元素是否在一个数组中
     * @param arr
     * @param val
     * @returns {boolean}
     */
    function contains(arr, val) {
        return indexOf(arr, val) != -1 ? true : false;
    }
    var nullText = "未知";

    var colors = ["#00CBD2", "#00B7E8", "#00A2FE"];

    var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });

    var total = 0;
    var groupTotal = {};
    var groupRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select distinct nvl(" + groupType + ",'" + nullText + "') as GROUP_NAME from V_LATEST_PRODUCT_SUBMIT" });
    for (var i = 0; i < groupRs.rows.length; i++) {
        var groupName = groupRs.rows[i]['GROUP_NAME'];
        groupTotal[groupName] = 0;
    }
    var modelDatas = [];

    //查询总数
    for (var i = 0; i < modelRs.data.length; i++) {
        var obj = {};
        var model = modelRs.data[i];
        var modelId = model.TREEID;
        var modelName = model.NODENAME;

        var submitListSql = me.GetSubmitListSql({
            treeId: modelId,
            startDate: startDate,
            endDate: endDate
        });

        var totalCountSql = "select count(*) as COUNT from (" + submitListSql + ")";

        var totalCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalCountSql });

        var totalGroupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;

        var totalGroupCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalGroupCountSql });

        for (var j = 0; j < totalGroupCountRs.rows.length; j++) {
            var group = totalGroupCountRs.rows[j];
            var groupName = group["GROUP_NAME"];
            var groupCount = group["COUNT"];
            groupTotal[groupName] = groupTotal[groupName] + groupCount;
        }

        var totalCount = totalCountRs.rows[0].COUNT;
        total += totalCount;
        obj.modelName = modelName;
        obj.modelId = modelId;
        obj.modelCount = totalCount;
        modelDatas.push(obj);
    }

    //降序排序
    modelDatas.sort(function (x, y) {
        return y.modelCount - x.modelCount;
    });
    if (modelDatas.length > 10) {
        modelDatas = modelDatas.slice(0, 10);
    }

    var gropuTypes = [];
    //查询分类数量
    for (var i = 0; i < modelDatas.length; i++) {
        var model = modelDatas[i];
        var modelId = model.modelId;
        var modelName = model.modelName;

        var submitListSql = me.GetSubmitListSql({
            treeId: modelId,
            startDate: startDate,
            endDate: endDate
        });
        var groupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;
        var groupCountRs = Things["Thing.DB.Oracle"].RunQuery({
            sql: groupCountSql
        });
        var modelGroups = [];
        for (var j = 0; j < groupCountRs.rows.length; j++) {
            var group = groupCountRs.rows[j];
            var groupName = group["GROUP_NAME"];
            if (!contains(gropuTypes, groupName)) {
                gropuTypes.push(groupName);
            }
            var groupCount = group["COUNT"];
            modelGroups.push({
                groupName: groupName,
                groupCount: groupCount
            });
        }
        model.modelGroups = modelGroups;
    }

    var modelNames = [], modelCounts = {};
    for (var i = 0; i < gropuTypes.length; i++) {
        modelCounts[gropuTypes[i]] = [];
    }
    for (var i = 0; i < modelDatas.length; i++) {
        modelNames.push(modelDatas[i].modelName + '~~~' + modelDatas[i].modelId);
        var modelGroups = modelDatas[i].modelGroups;
        for (var j = 0; j < modelGroups.length; j++) {
            var groupName = modelGroups[j].groupName;
            var groupCount = modelGroups[j].groupCount;
            modelCounts[groupName].push(groupCount);
        }
    }
    var series = [], seriesIndex = 0;
    for (var key in modelCounts) {
        var s = {
            name: key,
            data: modelCounts[key],
            type: 'bar',
            showBackground: true,
            stack: 'total',
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            label: {
                show: true,
                textStyle: {
                    color: 'white'
                }
            },
            // itemStyle: {
            //     color: colors[seriesIndex]
            // },
            barWidth: 40
        };
        series.push(s);
        seriesIndex++;
    }

    if (seriesIndex == 0) {
        series.push({
            data: [0],
            type: 'bar',
            showBackground: true,
            stack: 'total',
            backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
            },
            label: {
                show: true,
                textStyle: {
                    color: 'white'
                }
            },
            barWidth: 40
        });
    }
    var option = {
        tooltip: {
        },
        legend: {
            textStyle: {
                color: "#fff"
            }
        },
        xAxis: {
            type: 'category',
            data: modelNames,
            axisLabel: {
                rotate: 30,
            },
            axisLine: {
                lineStyle: {
                    color: 'white'
                }
            }
        },
        yAxis: {
            show: true,
            // 设置坐标轴的样式
            axisLine: {
                lineStyle: {
                    color: 'white'
                }
            },
            axisLabel: {
                fontSize: 14
            },
            type: 'value',
            splitLine: {
                show: true,
                lineStyle: {
                    color: "#141D54"
                }
            }
        },
        series: series
    };

    var numText = "";
    for (var key in groupTotal) {
        numText += key + ":" + groupTotal[key] + " "
    }
    res.data = {
        names: modelNames,
        counts: modelCounts,
        option: option,
        total: total,
        groupTotal: groupTotal,
        numText: numText
    };
    res.success = true;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;