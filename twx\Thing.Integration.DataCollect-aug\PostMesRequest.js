/**
 * @definition    PostMesRequest    {"category":"ext"}
 * @description   发送MES请求  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    reqUrl    请求URL
 * @param    {STRING}    processCode    过程代码
 * @param    {STRING}    category    数据类别
 * @param    {STRING}    entype    文件类型编码
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!reqUrl || reqUrl === "") {
        throw "请求URL不能为空";
    }
    if (!processCode || processCode === "") {
        throw "过程代码不能为空";
    }
    if (!category || category === "") {
        throw "数据类别不能为空";
    }
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }

    // 构建SOAP请求内容
    var content = [
        '<?xml version="1.0" encoding="utf-8"?>',
        '<soap:Envelope xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"',
        '                xmlns:xsd="http://www.w3.org/2001/XMLSchema"',
        '                xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/">',
        '    <soap:Body>',
        '        <GetProcessListInfo xmlns="http://tempuri.org/">',
        '            <xmlSend>&lt;Request&gt;',
        '                &lt;ProcessCode&gt;' + processCode + '&lt;/ProcessCode&gt;',
        '                &lt;Category&gt;' + category + '&lt;/Category&gt;',
        '                &lt;Type&gt;' + entype + '&lt;/Type&gt;',
        '            &lt;/Request&gt;</xmlSend>',
        '        </GetProcessListInfo>',
        '    </soap:Body>',
        '</soap:Envelope>'
    ].join('\n');

    // 构建请求参数
    var params = {
        headers: {
            "Content-Type": "text/xml; charset=utf-8"
        },
        url: reqUrl,
        timeout: 120000, // 120秒超时
        content: content
    };

    // 发送请求
    var resultXml = Resources["ContentLoaderFunctions"].PostXML(params);

    // 验证响应
    if (!resultXml) {
        throw "MES系统返回空响应";
    }

    // 解析返回的XML
    var contentXml = resultXml.*:: Body.*:: GetProcessListInfoResponse;
    if (!contentXml) {
        throw "无法解析MES响应XML结构";
    }

    var responseXml = String(contentXml.*:: GetProcessListInfoResult);
    if (!responseXml || responseXml === "") {
        throw "MES响应结果为空";
    }

    // 提取Response部分
    var startIndex = responseXml.indexOf("<Response");
    var endIndex = responseXml.indexOf("</Response>") + 11;

    if (startIndex === -1 || endIndex === -1) {
        throw "MES响应格式不正确，缺少Response标签";
    }

    var finalXml = responseXml.substring(startIndex, endIndex);

    res.success = true;
    res.data = {
        processCode: processCode,
        category: category,
        entype: entype,
        responseXml: finalXml
    };
    res.msg = "MES请求发送成功";

    // 记录请求信息
    logger.info("PostMesRequest-请求成功，processCode=" + processCode + "，category=" + category + "，entype=" + entype);

} catch (error) {
    res.success = false;
    var msg = "PostMesRequest-MES请求失败，原因：" + error;
    logger.error(msg + "，processCode=" + processCode + "，category=" + category + "，entype=" + entype);
    res.msg = msg;
}

result = res;
