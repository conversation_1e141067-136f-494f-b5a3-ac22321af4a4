/**
 * @definition    ProcessNodeGroup    {"category":"ext"}
 * @description   处理节点组数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    reqUrl    MES请求URL
 * @param    {JSON}    nodeGroup    节点组数据
 * @param    {NUMBER}    treeid    树节点ID
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!reqUrl || reqUrl === "") {
        throw "MES请求URL不能为空";
    }
    if (!nodeGroup || !nodeGroup.items || nodeGroup.items.length === 0) {
        throw "节点组数据不能为空";
    }
    
    var processedCount = 0;
    var refTreeId = nodeGroup.refTreeId;
    var tableType = nodeGroup.tableType;
    
    // 获取表名映射
    var tableMappingResult = me.GetTableMapping({
        category: tableType
    });
    
    if (!tableMappingResult.success) {
        throw "获取表名映射失败：" + tableMappingResult.msg;
    }
    
    //存放同步结果数据的表名
    var resultTableName = tableMappingResult.data.tableType;
    
    // 按文件类型分组处理
    var fileTypeGroups = {};
    for (var i = 0; i < nodeGroup.items.length; i++) {
        var item = nodeGroup.items[i];
        var fileType = item.fileType;
        
        if (!fileTypeGroups[fileType]) {
            fileTypeGroups[fileType] = [];
        }
        fileTypeGroups[fileType].push(item);
    }
    
    // 处理每个文件类型组
    for (var fileType in fileTypeGroups) {
        var items = fileTypeGroups[fileType];
        
        try {
            // 获取文件类型映射
            var fileTypeMappingResult = me.GetFileTypeMapping({
                fileType: fileType
            });
            
            if (!fileTypeMappingResult.success || !fileTypeMappingResult.data.isValid) {
                logger.warn("ProcessNodeGroup-跳过无效文件类型：" + fileType);
                continue;
            }
            
            var entype = fileTypeMappingResult.data.entype;
            
            // 发送MES请求
            var mesRequestResult = me.PostMesRequest({
                reqUrl: reqUrl,
                processCode: String(refTreeId),
                category: tableType,
                entype: entype
            });
            
            if (!mesRequestResult.success) {
                logger.error("ProcessNodeGroup-MES请求失败：" + mesRequestResult.msg + "，文件类型=" + fileType);
                continue;
            }
            
            // 解析MES响应
            var parseResult = me.ParseMesResponse({
                responseXml: mesRequestResult.data.responseXml,
                entype: entype
            });
            
            if (!parseResult.success) {
                logger.error("ProcessNodeGroup-解析MES响应失败：" + parseResult.msg + "，文件类型=" + fileType);
                continue;
            }
            
            var recordList = parseResult.data;
            if (recordList.length === 0) {
                logger.info("ProcessNodeGroup-MES返回空数据，文件类型=" + fileType);
                continue;
            }
            
            // 处理记录数据
            var processResult = me.ProcessRecordList({
                recordList: recordList,
                entype: entype,
                resultTableName: resultTableName,
                nodeData: items[0], // 使用第一个节点的数据作为基础信息
                treeid: treeid
            });
            
            if (processResult.success) {
                processedCount += processResult.data.processedCount;
            } else {
                logger.error("ProcessNodeGroup-处理记录数据失败：" + processResult.msg + "，文件类型=" + fileType);
            }
            
        } catch (fileTypeError) {
            logger.error("ProcessNodeGroup-处理文件类型异常：" + fileTypeError + "，文件类型=" + fileType);
        }
    }
    
    res.success = true;
    res.data = {
        processedCount: processedCount,
        refTreeId: refTreeId,
        tableType: tableType,
        fileTypeCount: Object.keys(fileTypeGroups).length
    };
    res.msg = "节点组处理完成，处理" + processedCount + "条记录";
    
    // 记录处理信息
    logger.info("ProcessNodeGroup-节点组处理完成，refTreeId=" + refTreeId + "，处理=" + processedCount);
    
} catch (error) {
    res.success = false;
    var msg = "ProcessNodeGroup-处理节点组失败，原因：" + error;
    logger.error(msg + "，refTreeId=" + (nodeGroup ? nodeGroup.refTreeId : "unknown"));
    res.msg = msg;
}

result = res;
