/**
 * @definition    UtilService    {"category":"ext"}
 * @description   工具服务，提供通用的工具函数  wanghq 2025年8月21日19:40:00
 * @implementation    {Script}
 *
 * @param    {STRING}    operation    操作类型
 * @param    {JSON}    params    参数
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: null
};

try {
    var operation = operation || "";
    var params = params || {};

    if (operation === "") {
        throw "操作类型不能为空";
    }

    logger.info("UtilService-执行操作：" + operation);

    switch (operation) {
        case "formatTime":
            result.data = formatProcessingTime(params.msec);
            result.success = true;
            result.msg = "时间格式化成功";
            break;

        case "updateResultStatus":
            updateResultStatus(params.treeid);
            result.success = true;
            result.msg = "状态更新成功";
            break;

        case "getTableType":
            result.data = getTableType(params.category);
            result.success = true;
            result.msg = "表类型获取成功";
            break;

        default:
            throw "不支持的操作类型：" + operation;
    }

} catch (error) {
    result.success = false;
    var msg = "UtilService-操作失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;

// 格式化处理时间
function formatProcessingTime(msec) {
    try {
        return me.msecToTime({
            msec: msec
        });
    } catch (error) {
        logger.error("UtilService-格式化处理时间失败：" + error);
        return msec + "ms";
    }
}

// 更新结果状态
function updateResultStatus(treeid) {
    try {
        me.UpdateResultStatus({
            treeid: treeid
        });
        logger.info("UtilService-更新结果状态完成");
    } catch (error) {
        logger.error("UtilService-更新结果状态失败：" + error);
    }
}

// 获取表类型
function getTableType(category) {
    switch (category) {
        case "设计":
            return "DESIGN_DATA_RESULT";
        case "工艺":
            return "CRAFT_DATA_RESULT";
        case "质量综合":
            return "QUALITY_CONTROL_RESULT";
        case "过程控制":
            return "PROCESS_CONTROL_RESULT";
        default:
            return "";
    }
}