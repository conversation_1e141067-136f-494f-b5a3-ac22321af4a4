/**
 * @definition    StructuralAssemblyService    {"category":"ext"}
 * @description   结构装配文件解析服务，处理特殊的结构装配文件解析逻辑  wanghq 2025年8月21日19:32:00
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID
 * @param    {STRING}    fileName    文件名
 * @param    {STRING}    filePath    文件路径
 * @param    {NUMBER}    resultId    结果ID
 * @param    {STRING}    dataId    数据ID
 * @param    {STRING}    productId    产品ID
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: null
};

try {
    var treeid = treeid || 0;
    var fileName = fileName || "";
    var filePath = filePath || "";
    var resultId = resultId || 0;
    var dataId = dataId || "";
    var productId = productId || "";

    if (fileName === "" || filePath === "") {
        throw "文件名和文件路径不能为空";
    }

    logger.info("StructuralAssemblyService-处理结构装配文件：" + fileName);

    // 检查是否为结构装配节点
    var isStructuralAssembly = me.isStructuralAssembly({
        treeid: treeid
    });

    if (isStructuralAssembly !== 'true') {
        result.success = true;
        result.msg = "非结构装配节点，无需特殊处理";
        result = result;
        return;
    }

    // 检查是否包含"精度测量"关键词
    if (fileName.indexOf('精度测量') === -1) {
        result.success = true;
        result.msg = "文件不包含精度测量关键词，无需解析";
        result = result;
        return;
    }

    logger.info("StructuralAssemblyService-开始解析结构装配文件，文件名：" + fileName);

    // 获取文件上传路径
    var fileUploadPath = Things["Thing.Fn.SystemDic"].getFileUploadPath();
    var fullFilePath = fileUploadPath + filePath;

    // 获取模型信息
    var model = me.getModelByTreeid({
        treeid: treeid
    });

    // 清理旧数据
    var cleanSql = "DELETE FROM TJFX_STRUCTURALASSEMBLY WHERE RESULT_ID = " + resultId;
    Things['Thing.DB.Oracle'].RunCommand({
        sql: cleanSql
    });

    // 解析结构装配文件
    var parseResult = me.parseStructuralAssemblyFile({
        ref_dpid: resultId,  // 使用结果ID作为引用ID
        data_id: dataId,
        result_id: resultId,
        product_id: productId,
        model: model,
        thisFilePath: fullFilePath
    });

    result.data = parseResult;
    result.success = true;
    result.msg = "结构装配文件解析完成";
    logger.info("StructuralAssemblyService-结构装配文件解析成功");

} catch (error) {
    result.success = false;
    var msg = "StructuralAssemblyService-结构装配文件解析失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;