/**
 * @definition    GetSystemConfig    {"category":"ext"}
 * @description   获取系统配置信息  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    configName    配置名称
 * @param    {STRING}    parentName    父级配置名称    {"aspect.defaultValue":"系统配置"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!configName || configName === "") {
        throw "配置名称不能为空";
    }
    
    // 调用系统字典服务获取配置
    var configValue = Things["Thing.Fn.SystemDic"].getKeyByNames({
        name: configName,
        pname: parentName || "系统配置"
    });
    
    // 验证配置是否存在
    if (configValue === null || configValue === undefined || configValue === "") {
        throw "未找到配置项：" + configName;
    }
    
    res.success = true;
    res.data = {
        configName: configName,
        parentName: parentName || "系统配置",
        configValue: configValue
    };
    res.msg = "获取配置成功";
    
} catch (error) {
    res.success = false;
    var msg = "GetSystemConfig-获取配置失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
