/**
 * @definition    GetFileNameColumn    {"category":"ext"}
 * @description   根据文件类型获取文件名称列名  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    entype    文件类型编码
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }

    var columnName = "";

    // 根据文件类型映射列名
    if (entype === "TechCard") {
        columnName = "AsmTaskName";
    } else if (entype === "CheckCard") {
        columnName = "CheckCardName";
    } else if (entype === "TechProblem") {
        columnName = "BillCode";
    } else if (entype === "Photo") {
        columnName = "PhotoName";
    } else if (entype === "ScrapNotice") {
        columnName = "BillCode";
    } else if (entype === "MaterialDelivery") {
        columnName = "BillCode";
    } else if (entype === "ProductSubmit") {
        columnName = "BillCode";
    } else if (entype === "UndeliveredProduct") {
        columnName = "BillCode";
    } else if (entype === "ProductOutIn") {
        columnName = "BillCode";
    } else if (entype === "UniversalTrackingCard") {
        columnName = "TaskName";
    } else {
        // 默认使用BillCode
        columnName = "BillCode";
        logger.warn("GetFileNameColumn-未知文件类型，使用默认列名：" + entype);
    }

    res.success = true;
    res.data = {
        entype: entype,
        columnName: columnName
    };
    res.msg = "获取文件名称列名成功";

} catch (error) {
    res.success = false;
    var msg = "GetFileNameColumn-获取文件名称列名失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
