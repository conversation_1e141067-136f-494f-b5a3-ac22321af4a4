/**
 * @definition    ProcessRecordList    {"category":"ext"}
 * @description   批量处理记录列表  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {INFOTABLE}    recordList    记录列表
 * @param    {STRING}    entype    文件类型编码
 * @param    {STRING}    resultTableName    结果表名
 * @param    {JSON}    nodeData    节点数据
 * @param    {NUMBER}    treeid    树节点ID
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (!recordList || recordList.length === 0) {
        throw "记录列表为空";
    }
    
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }
    
    if (!resultTableName || resultTableName === "") {
        throw "结果表名不能为空";
    }
    
    if (!nodeData) {
        throw "节点数据不能为空";
    }
    
    // 1. 构建源ID列表用于批量查询已存在数据
    var sourceIdTable = Resources["InfoTableFunctions"].CreateInfoTableFromDataShape({
        infoTableName: "SourceIdList",
        dataShapeName: "StringList"
    });
    
    for (var i = 0; i < recordList.length; i++) {
        var record = recordList[i];
        if (record.sourceId && record.sourceId !== "") {
            var sourceIdWithTable = record.sourceId + '_' + resultTableName;
            sourceIdTable.AddRow({ name: sourceIdWithTable });
        }
    }
    
    // 2. 批量查询已存在数据
    var existingDataResult = me.QueryExistingData({
        entype: entype,
        sourceIds: sourceIdTable
    });
    
    if (!existingDataResult.success) {
        throw "批量查询已存在数据失败：" + existingDataResult.msg;
    }
    
    var existingDataMap = existingDataResult.data;
    
    // 3. 分类处理记录：新增和更新
    var newRecords = [];
    var updateRecords = [];
    var processedCount = 0;
    
    for (var j = 0; j < recordList.length; j++) {
        var record = recordList[j];
        var sourceIdWithTable = record.sourceId + '_' + resultTableName;
        
        // 跳过无效记录
        if (!record.sourceId || record.sourceId === "") {
            continue;
        }
        
        // 处理文件下载
        var fileProcessResult = me.ProcessFileByType({
            entype: entype,
            record: record
        });
        
        if (!fileProcessResult.success) {
            logger.warn("ProcessRecordList-文件处理失败：" + fileProcessResult.msg + "，sourceId=" + record.sourceId);
            continue;
        }
        
        var fileInfo = fileProcessResult.data;
        
        // 构建记录数据
        var recordData = {
            sourceId: sourceIdWithTable,
            nodeCode: nodeData.nodeCode,
            nodeName: nodeData.nodeCode,
            fileName: record.fileName,
            fileType: nodeData.fileType,
            gatheringMethod: "自动采集",
            sourceSystem: "MES",
            deliveryState: nodeData.deliveryState,
            securityLevel: record.securityLevel,
            stateCheck: "未确认",
            status: "new",
            productId: record.productId,
            fileInfo: fileInfo,
            rawRecord: record
        };
        
        // 判断是新增还是更新
        if (existingDataMap[sourceIdWithTable]) {
            recordData.existingData = existingDataMap[sourceIdWithTable];
            updateRecords.push(recordData);
        } else {
            newRecords.push(recordData);
        }
        
        processedCount++;
    }
    
    // 4. 批量处理新增记录
    if (newRecords.length > 0) {
        var insertResult = me.BatchProcessNewRecords({
            records: newRecords,
            resultTableName: resultTableName,
            entype: entype,
            treeid: treeid
        });
        
        if (!insertResult.success) {
            logger.error("ProcessRecordList-批量处理新增记录失败：" + insertResult.msg);
        }
    }
    
    // 5. 批量处理更新记录
    if (updateRecords.length > 0) {
        var updateResult = me.BatchProcessUpdateRecords({
            records: updateRecords,
            resultTableName: resultTableName,
            entype: entype,
            treeid: treeid
        });
        
        if (!updateResult.success) {
            logger.error("ProcessRecordList-批量处理更新记录失败：" + updateResult.msg);
        }
    }
    
    res.success = true;
    res.data = {
        processedCount: processedCount,
        newRecordsCount: newRecords.length,
        updateRecordsCount: updateRecords.length
    };
    res.msg = "批量处理记录完成，处理" + processedCount + "条，新增" + newRecords.length + "条，更新" + updateRecords.length + "条";
    
    // 记录处理信息
    logger.info("ProcessRecordList-批量处理完成，entype=" + entype + "，处理=" + processedCount + "，新增=" + newRecords.length + "，更新=" + updateRecords.length);
    
} catch (error) {
    res.success = false;
    var msg = "ProcessRecordList-批量处理记录失败，原因：" + error;
    logger.error(msg + "，entype=" + entype);
    res.msg = msg;
}

result = res;
