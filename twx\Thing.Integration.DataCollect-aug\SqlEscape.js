/**
 * @definition    SqlEscape    {"category":"ext"}
 * @description   SQL转义处理  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    value    需要转义的值
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (value === null || value === undefined) {
        res.success = true;
        res.data = {
            originalValue: value,
            escapedValue: ""
        };
        res.msg = "空值转义完成";
        result = res;
        return;
    }
    
    // 转换为字符串
    var stringValue = String(value);
    
    // SQL转义：将单引号替换为两个单引号
    var escapedValue = stringValue.replace(/'/g, "''");
    
    res.success = true;
    res.data = {
        originalValue: stringValue,
        escapedValue: escapedValue
    };
    res.msg = "SQL转义完成";
    
} catch (error) {
    res.success = false;
    var msg = "SqlEscape-SQL转义失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
