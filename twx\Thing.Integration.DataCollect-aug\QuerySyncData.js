/**
 * @definition    QuerySyncData    {"category":"ext"}
 * @description   批量查询待同步数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeid    树节点ID    {"aspect.defaultValue":"0"}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: [] };

try {
    // 构建查询SQL
    var sql = "select a.*,b.REFTREEID from alldatalistview a,DATA_PACKAGE b " +
              "where a.NODECODE=b.ID and a.gathering_method='自动采集' and a.collectstatus='1'";
    
    // 如果指定了treeid，添加过滤条件
    if (treeid !== 0) {
        sql += " and b.REFTREEID=" + treeid;
    }
    
    // 添加排序，确保处理顺序一致
    sql += " order by b.REFTREEID, a.NODECODE";
    
    // 执行查询
    var queryResult = Things['Thing.DB.Oracle'].RunQuery({
        sql: sql
    });
    
    // 检查查询结果
    if (queryResult && queryResult.rows) {
        var dataList = [];
        for (var i = 0; i < queryResult.rows.length; i++) {
            var row = queryResult.rows[i];
            dataList.push({
                nodeCode: row.NODECODE,
                refTreeId: row.REFTREEID,
                fileType: row.FILE_TYPE,
                tableType: row.TABLETYPE,
                deliveryState: row.DELIVERY_STATE,
                collectStatus: row.COLLECTSTATUS,
                gatheringMethod: row.GATHERING_METHOD
            });
        }
        
        res.success = true;
        res.data = dataList;
        res.msg = "查询待同步数据成功，共" + dataList.length + "条记录";
        
        // 记录查询信息
        logger.info("QuerySyncData-查询待同步数据成功，treeid=" + treeid + "，记录数=" + dataList.length);
        
    } else {
        res.success = true;
        res.data = [];
        res.msg = "未找到待同步数据";
    }
    
} catch (error) {
    res.success = false;
    var msg = "QuerySyncData-查询待同步数据失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
