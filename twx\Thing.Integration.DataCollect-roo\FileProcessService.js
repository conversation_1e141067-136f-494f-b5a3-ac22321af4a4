/**
 * @definition    FileProcessService    {"category":"ext"}
 * @description   文件处理服务，负责下载和处理不同类型的文件  wanghq 2025年8月21日19:31:45
 * @implementation    {Script}
 *
 * @param    {STRING}    entype    文件类型编码
 * @param    {JSON}    record    MES返回的记录数据
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: {
        fileName: "",
        filePath: "",
        fileFormat: ""
    }
};

try {
    var entype = entype || "";
    var record = record || {};

    if (entype === "") {
        throw "文件类型编码不能为空";
    }
    if (typeof record !== "object") {
        throw "记录数据格式错误";
    }

    logger.info("FileProcessService-处理文件，类型：" + entype);

    // 根据文件类型进行不同的处理
    switch (entype) {
        case "CheckCard":
            // 处理CheckCard类型（PDF和Excel文件）
            result.data = processCheckCardFiles(record);
            break;

        case "Photo":
            // 处理图片类型
            result.data = processPhotoFile(record);
            break;

        case "TechCard":
        case "TechProblem":
        case "ScrapNotice":
        case "ProductSubmit":
        case "UndeliveredProduct":
        case "UniversalTrackingCard":
            // 处理普通文件类型
            result.data = processNormalFile(record);
            break;

        default:
            throw "不支持的文件类型：" + entype;
    }

    result.success = true;
    result.msg = "文件处理成功";

} catch (error) {
    result.success = false;
    var msg = "FileProcessService-文件处理失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;

// 处理CheckCard类型文件（PDF和Excel）
function processCheckCardFiles(record) {
    var result = {
        pdfFileName: "",
        pdfFilePath: "",
        pdfFileFormat: "pdf",
        excelFileName: "",
        excelFilePath: "",
        excelFileFormat: "xlsx"
    };

    try {
        // 处理PDF文件
        if (record.PDFDownLoadURL) {
            var pdfResult = me.downloadFileTest({
                fileName: "",
                filePath: "",
                url: record.PDFDownLoadURL
            });
            if (pdfResult.success) {
                result.pdfFileName = pdfResult.fileName;
                result.pdfFilePath = pdfResult.filePath;
                result.pdfFileFormat = pdfResult.fileFormat || "pdf";
            }
        }

        // 处理Excel文件
        if (record.ExcelDownLoadURL) {
            var excelResult = me.downloadFileTest({
                fileName: "",
                filePath: "",
                url: record.ExcelDownLoadURL
            });
            if (excelResult.success) {
                result.excelFileName = excelResult.fileName;
                result.excelFilePath = excelResult.filePath;
                result.excelFileFormat = excelResult.fileFormat || "xlsx";
            }
        }

        logger.info("FileProcessService-CheckCard文件处理完成，PDF：" + result.pdfFileName + "，Excel：" + result.excelFileName);
    } catch (error) {
        logger.error("FileProcessService-CheckCard文件处理异常：" + error);
    }

    return result;
}

// 处理图片类型文件
function processPhotoFile(record) {
    var result = {
        fileName: "",
        filePath: "",
        fileFormat: ""
    };

    try {
        var downloadUrl = record.DownLoadURL || "";

        if (downloadUrl !== "") {
            result.filePath = downloadUrl;

            // 从URL中提取文件格式
            var lastDotIndex = downloadUrl.lastIndexOf('.');
            var beforeLastSlash = downloadUrl.lastIndexOf('_');

            if (lastDotIndex > beforeLastSlash) {
                result.fileFormat = downloadUrl.substring(lastDotIndex + 1).toLowerCase();

                // 验证文件格式
                var validFormats = ['jpg', 'JPG', 'png', 'PNG', 'gif', 'GIF', 'bpm', 'BPM', 'mp4', 'MP4', 'rmvb', 'RMVB', 'flv', 'FLV', 'mov', 'MOV', 'avi', 'AVI'];
                if (validFormats.indexOf(result.fileFormat) === -1) {
                    result.fileFormat = "";
                    logger.warn("FileProcessService-不支持的图片格式：" + result.fileFormat);
                }
            }
        }

        logger.info("FileProcessService-图片文件处理完成，格式：" + result.fileFormat);
    } catch (error) {
        logger.error("FileProcessService-图片文件处理异常：" + error);
    }

    return result;
}

// 处理普通文件类型
function processNormalFile(record) {
    var result = {
        fileName: "",
        filePath: "",
        fileFormat: ""
    };

    try {
        var downloadUrl = record.DownLoadURL || "";

        if (downloadUrl !== "") {
            var fileResult = me.downloadFileTest({
                fileName: "",
                filePath: "",
                url: downloadUrl
            });

            if (fileResult.success) {
                result.fileName = fileResult.fileName;
                result.filePath = fileResult.filePath;
                result.fileFormat = fileResult.fileFormat || "";
            }
        }

        logger.info("FileProcessService-普通文件处理完成，文件名：" + result.fileName);
    } catch (error) {
        logger.error("FileProcessService-普通文件处理异常：" + error);
    }

    return result;
}