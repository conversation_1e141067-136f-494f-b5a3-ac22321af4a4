/**
 * @definition    DataSaveService    {"category":"ext"}
 * @description   数据保存服务，负责将数据保存到结果表  wanghq 2025年8月21日19:38:10
 * @implementation    {Script}
 *
 * @param    {JSON}    params    保存参数，包含行数据、记录、文件数据等
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: {
        resultId: 0,
        pdfResultId: 0,
        excelResultId: 0
    }
};

try {
    var params = params || {};
    var row = params.row;
    var record = params.record;
    var fileData = params.fileData || {};
    var entype = params.entype || "";
    var isExists = params.isExists || false;
    var existingData = params.existingData;

    if (!row || !record || entype === "") {
        throw "参数不完整";
    }

    logger.info("DataSaveService-保存数据，类型：" + entype + "，是否已存在：" + isExists);

    // 获取文件名列
    var fileNameClo = me.getTagFileName({
        type: entype
    });

    if (!fileNameClo) {
        throw "无法获取文件名列名";
    }

    // 构建表类型
    var tableType = getTableType(row.TABLETYPE);
    if (!tableType) {
        throw "不支持的数据类别：" + row.TABLETYPE;
    }

    // 构建数据ID
    var dataId = record.Id + '_' + tableType;

    var baseParams = {
        type: tableType,
        NODECODE: row.NODECODE,
        NODENAME: row.NODECODE,
        FILE_NUMBER: "",
        FILE_NAME: record[fileNameClo] || "",
        FILE_TYPE: row.FILE_TYPE,
        GATHERING_METHOD: "自动采集",
        SOURCE_SYSTEM: "MES",
        DELIVERY_STATE: row.DELIVERY_STATE,
        SECURITY_LEVEL: record.SecurityLevel || "",
        STATE_CHECK: "未确认",
        STATUS: 'new',
        productId: record.ProductId || ""
    };

    if (entype === "CheckCard") {
        // 处理CheckCard类型（PDF和Excel）
        result.data.pdfResultId = saveCheckCardFile(baseParams, fileData.pdfFileName, fileData.pdfFilePath, fileData.pdfFileFormat, "PDF", isExists, existingData);
        result.data.excelResultId = saveCheckCardFile(baseParams, fileData.excelFileName, fileData.excelFilePath, fileData.excelFileFormat, "EXCEL", isExists, existingData);
    } else {
        // 处理其他类型
        var fileFormat = fileData.fileFormat || "";
        var fileName = fileData.fileName || "";
        var filePath = fileData.filePath || "";

        if (entype === "MaterialDelivery") {
            // 特殊处理MaterialDelivery类型
            var materieResultId = me.getMaterieResultId({
                tableType: tableType,
                nodecode: row.NODECODE,
                materielCode: record[fileNameClo]
            });

            if (materieResultId === -1) {
                result.data.resultId = me.addDataToResultTable({
                    type: tableType,
                    NODECODE: row.NODECODE,
                    NODENAME: row.NODECODE,
                    FILE_NUMBER: "",
                    FILE_NAME: record[fileNameClo],
                    FILE_TYPE: row.FILE_TYPE,
                    GATHERING_METHOD: "自动采集",
                    SOURCE_SYSTEM: "MES",
                    DELIVERY_STATE: row.DELIVERY_STATE,
                    SECURITY_LEVEL: record.SecurityLevel,
                    FILEPATH: filePath,
                    FILENAME: fileName,
                    STATE_CHECK: "未确认",
                    STATUS: 'new',
                    productId: record.ProductId || "",
                    FILE_FORMAT: fileFormat
                });
            } else {
                result.data.resultId = materieResultId;
            }
        } else {
            // 普通文件类型
            result.data.resultId = me.addDataToResultTable({
                type: tableType,
                NODECODE: row.NODECODE,
                NODENAME: row.NODECODE,
                FILE_NUMBER: "",
                FILE_NAME: record[fileNameClo],
                FILE_TYPE: row.FILE_TYPE,
                GATHERING_METHOD: "自动采集",
                SOURCE_SYSTEM: "MES",
                DELIVERY_STATE: row.DELIVERY_STATE,
                SECURITY_LEVEL: record.SecurityLevel,
                FILEPATH: filePath,
                FILENAME: fileName,
                STATE_CHECK: "未确认",
                STATUS: 'new',
                productId: record.ProductId || "",
                FILE_FORMAT: fileFormat
            });
        }
    }

    // 如果是更新操作，删除旧的XML数据
    if (isExists) {
        var soId = existingData.SOURCE_ID;
        Things['Thing.DB.Oracle'].RunCommand({
            sql: "DELETE FROM XMLDATA_" + entype + " WHERE SOURCE_ID='" + soId + "'"
        });
    }

    result.success = true;
    result.msg = "数据保存成功";

    logger.info("DataSaveService-数据保存成功，类型：" + entype);

} catch (error) {
    result.success = false;
    var msg = "DataSaveService-数据保存失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;

// 获取表类型
function getTableType(category) {
    switch (category) {
        case "设计":
            return "DESIGN_DATA_RESULT";
        case "工艺":
            return "CRAFT_DATA_RESULT";
        case "质量综合":
            return "QUALITY_CONTROL_RESULT";
        case "过程控制":
            return "PROCESS_CONTROL_RESULT";
        default:
            return "";
    }
}

// 保存CheckCard文件
function saveCheckCardFile(baseParams, fileName, filePath, fileFormat, fileType, isExists, existingData) {
    try {
        if (isExists) {
            var resultId = fileType === "PDF" ? existingData.PDF_RESULT_ID : existingData.EXCEL_RESULT_ID;
            if (resultId) {
                // 删除旧数据
                Things['Thing.DB.Oracle'].RunCommand({
                    sql: 'DELETE FROM ' + baseParams.type + ' WHERE id=' + resultId
                });

                // 更新数据
                me.updateDataToResultTable({
                    id: resultId,
                    type: baseParams.type,
                    NODECODE: baseParams.NODECODE,
                    NODENAME: baseParams.NODENAME,
                    FILE_NUMBER: baseParams.FILE_NUMBER,
                    FILE_NAME: baseParams.FILE_NAME,
                    FILE_TYPE: baseParams.FILE_TYPE,
                    GATHERING_METHOD: baseParams.GATHERING_METHOD,
                    SOURCE_SYSTEM: baseParams.SOURCE_SYSTEM,
                    DELIVERY_STATE: baseParams.DELIVERY_STATE,
                    SECURITY_LEVEL: baseParams.SECURITY_LEVEL,
                    FILEPATH: filePath,
                    FILENAME: fileName,
                    STATE_CHECK: baseParams.STATE_CHECK,
                    STATUS: baseParams.STATUS,
                    productId: baseParams.productId,
                    FILE_FORMAT: fileFormat
                });
            }
            return resultId;
        } else {
            // 新增数据
            return me.addDataToResultTable({
                type: baseParams.type,
                NODECODE: baseParams.NODECODE,
                NODENAME: baseParams.NODENAME,
                FILE_NUMBER: baseParams.FILE_NUMBER,
                FILE_NAME: baseParams.FILE_NAME,
                FILE_TYPE: baseParams.FILE_TYPE,
                GATHERING_METHOD: baseParams.GATHERING_METHOD,
                SOURCE_SYSTEM: baseParams.SOURCE_SYSTEM,
                DELIVERY_STATE: baseParams.DELIVERY_STATE,
                SECURITY_LEVEL: baseParams.SECURITY_LEVEL,
                FILEPATH: filePath,
                FILENAME: fileName,
                STATE_CHECK: baseParams.STATE_CHECK,
                STATUS: baseParams.STATUS,
                productId: baseParams.productId,
                FILE_FORMAT: fileFormat
            });
        }
    } catch (error) {
        logger.error("DataSaveService-保存CheckCard文件异常：" + error);
        return 0;
    }
}