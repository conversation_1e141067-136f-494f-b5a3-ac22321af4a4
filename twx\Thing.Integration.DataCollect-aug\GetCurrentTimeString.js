/**
 * @definition    GetCurrentTimeString    {"category":"ext"}
 * @description   获取当前时间字符串  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 获取当前时间
    var now = new Date();
    
    // 格式化时间字符串 yyyy-MM-dd HH:mm:ss
    var year = now.getFullYear();
    var month = (now.getMonth() + 1);
    var day = now.getDate();
    var hour = now.getHours();
    var minute = now.getMinutes();
    var second = now.getSeconds();
    
    // 补零处理
    if (month < 10) month = "0" + month;
    if (day < 10) day = "0" + day;
    if (hour < 10) hour = "0" + hour;
    if (minute < 10) minute = "0" + minute;
    if (second < 10) second = "0" + second;
    
    var timeString = year + "-" + month + "-" + day + " " + hour + ":" + minute + ":" + second;
    
    res.success = true;
    res.data = {
        timeString: timeString,
        timestamp: now.getTime()
    };
    res.msg = "获取当前时间成功";
    
} catch (error) {
    res.success = false;
    var msg = "GetCurrentTimeString-获取当前时间失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
