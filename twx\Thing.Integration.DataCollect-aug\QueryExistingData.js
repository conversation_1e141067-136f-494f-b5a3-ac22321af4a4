/**
 * @definition    QueryExistingData    {"category":"ext"}
 * @description   批量查询已存在数据  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {STRING}    entype    文件类型编码
 * @param    {JSON}    sourceIds    源ID列表
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: {} };

try {
    // 参数验证
    if (!entype || entype === "") {
        throw "文件类型编码不能为空";
    }

    if (!sourceIds || !sourceIds.length || sourceIds.length === 0) {
        throw "源ID列表为空";
    }

    // 构建IN子句的源ID列表
    var sourceIdList = [];
    for (var i = 0; i < sourceIds.length; i++) {
        var sourceId = sourceIds[i].SOURCE_ID;
        if (sourceId && sourceId !== "") {
            // SQL转义处理
            sourceId = String(sourceId).replace(/'/g, "''");
            sourceIdList.push("'" + sourceId + "'");
        }
    }

    if (sourceIdList.length === 0) {
        throw "有效源ID列表为空";
    }

    // 构建批量查询SQL
    var sql = "select SOURCE_ID, RESULT_ID, PDF_RESULT_ID, EXCEL_RESULT_ID " +
        "from XMLDATA_" + entype + " " +
        "where SOURCE_ID in (" + sourceIdList.join(",") + ")";

    // 执行查询
    var queryResult = Things["Thing.DB.Oracle"].RunQuery({
        sql: sql
    });

    // 构建结果映射
    var existingDataMap = {};
    if (queryResult && queryResult.rows) {
        for (var j = 0; j < queryResult.rows.length; j++) {
            var row = queryResult.rows[j];
            existingDataMap[row.SOURCE_ID] = {
                sourceId: row.SOURCE_ID,
                resultId: row.RESULT_ID,
                pdfResultId: row.PDF_RESULT_ID,
                excelResultId: row.EXCEL_RESULT_ID
            };
        }
    }

    res.success = true;
    res.data = existingDataMap;
    res.msg = "批量查询已存在数据成功，查询" + sourceIdList.length + "条，找到" + Object.keys(existingDataMap).length + "条已存在记录";

    // 记录查询信息
    logger.info("QueryExistingData-批量查询成功，entype=" + entype + "，查询数=" + sourceIdList.length + "，存在数=" + Object.keys(existingDataMap).length);

} catch (error) {
    res.success = false;
    var msg = "QueryExistingData-批量查询已存在数据失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
