/**
 * @definition    QueryModelSubmitCount
 * @description   查询产品交接单的数量 wanghq 2023年4月2日00:02:30
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 * @param    {STRING}    startDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    endDate        {"aspect.defaultValue":" "}
 * @param    {STRING}    username        {"aspect.defaultValue":" "}
 * @param    {STRING}    groupType        {"aspect.defaultValue":"ISCERTIFICATE"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var fileType = "产品交接单";
    var nullText = "未知";


    var modelRs = me.GetUserRoleModel({ username: username, isUseScreen: 1, treeId: treeId });

    var total = 0; //所有的总数
    var groupTotal = {}; //各个分类的总数
    var groupNames = [];
    var groupNamesAlias = [];
    var groupRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select distinct nvl(" + groupType + ",'" + nullText + "') as GROUP_NAME from V_LATEST_PRODUCT_SUBMIT  where " + groupType + " is not null" });
    for (var i = 0; i < groupRs.rows.length; i++) {
        var groupName = groupRs.rows[i]['GROUP_NAME'];
        groupTotal[groupName] = 0;
        groupNames.push(groupName);
        groupNamesAlias.push("'" + groupName + "' " + groupName);
    }

    var modelDatas = [];

    //没有查询条件的情况下查询统计数据库
    if (me.StrIsEmpty({ str: startDate }) && me.StrIsEmpty({ str: endDate })) {
        var modelArr = [];
        for (var i = 0; i < modelRs.data.length; i++) {
            modelArr.push(modelRs.data[i].TREEID);
        }
        var models = modelArr.join(",");
        var statParentType = fileType + "_" + groupType + "_";
        var querySql = "select * from (select a.MODEL_ID,a.STAT_COUNT,replace(a.STAT_TYPE,'" + statParentType + "','') as STAT_TYPE,b.NODENAME from (select * from MODEL_STATISTICS where MODEL_ID in (" + models + ") and STAT_TYPE like '" + statParentType + "%') a left join PHASE_MODEL b on a.MODEL_ID=b.TREEID) pivot (sum(STAT_COUNT) for stat_type in (" + groupNamesAlias.join(",") + "))";
        var statRs = Things['Thing.DB.Oracle'].RunQuery({ sql: querySql });

        for (var i = 0; i < statRs.rows.length; i++) {
            var stat = statRs.rows[i];
            var modelId = stat.MODEL_ID;
            var modelName = stat.NODENAME;
            var modelCount = 0;
            var modelGroupCount = {};
            for (var j = 0; j < groupNames.length; j++) {
                var groupName = groupNames[j];
                var groupCount = stat[groupName] || 0;
                groupTotal[groupName] = groupTotal[groupName] + groupCount;
                modelGroupCount[groupName] = groupCount;
                modelCount = modelCount + groupCount;
                total += modelCount;
            }
            var obj = {};
            obj.modelName = modelName;
            obj.modelId = modelId;
            obj.modelCount = modelCount;
            obj.modelGroupCount = modelGroupCount;
            if (modelCount > 0) {
                modelDatas.push(obj);
            }
        }
    } else {
        for (var i = 0; i < modelRs.data.length; i++) {
            var obj = {};
            var model = modelRs.data[i];
            var modelId = model.TREEID;
            var modelName = model.NODENAME;

            var submitListSql = me.GetSubmitListSql({
                treeId: modelId,
                startDate: startDate,
                endDate: endDate
            });

            var totalCountSql = "select count(*) as COUNT from (" + submitListSql + ")";

            var totalCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalCountSql });

            var totalGroupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;

            var totalGroupCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalGroupCountSql });
            var modelGroupCount = {};
            for (var x = 0; x < groupNames.length; x++) {
                var allGroupName = groupNames[x];
                var allGroupCount = 0;
                for (var j = 0; j < totalGroupCountRs.rows.length; j++) {
                    var group = totalGroupCountRs.rows[j];
                    var groupName = group["GROUP_NAME"];
                    if (groupName == allGroupName) {
                        allGroupCount = group["COUNT"];
                        break;
                    }
                }
                groupTotal[allGroupName] = groupTotal[allGroupName] + allGroupCount;
                modelGroupCount[allGroupName] = allGroupCount;
            }

            var totalCount = totalCountRs.rows[0].COUNT;
            total += totalCount;
            obj.modelName = modelName;
            obj.modelId = modelId;
            obj.modelCount = totalCount;
            obj.modelGroupCount = modelGroupCount;
            if (totalCount > 0) {
                modelDatas.push(obj);
            }

        }
    }

    //降序排序
    modelDatas.sort(function (x, y) {
        return y.modelCount - x.modelCount;
    });

    if (modelDatas.length <= 10) {
        for (var i = 0; i <= 10; i++) {
            if (modelDatas.length < i) {
                modelDatas.push({
                    modelName: '',
                    modelId: 0,
                    modelCount: '',
                    modelGroupCount: {}
                });
            }
        }
    }

    // if (modelDatas.length > 10) {
    //     modelDatas = modelDatas.slice(0, 10);
    // }

    var modelNames = [], modelCounts = {};
    for (var j = 0; j < groupNames.length; j++) {
        modelCounts[groupNames[j]] = [];
    }
    for (var i = 0; i < modelDatas.length; i++) {
        modelNames.push(modelDatas[i].modelName + '~~~' + modelDatas[i].modelId);
        var modelGroupCount = modelDatas[i].modelGroupCount;
        for (var groupName in modelGroupCount) {
            modelCounts[groupName].push(modelGroupCount[groupName]);
        }
    }
    var optionRs = me.GetSubmitChartOption({
        itemCounts: modelCounts,
        itemNames: modelNames
    });

    var numText = "";
    for (var key in groupTotal) {
        numText += key + ":" + groupTotal[key] + " "
    }
    res.data = {
        names: modelNames,
        counts: modelCounts,
        option: optionRs.data,
        total: total,
        groupTotal: groupTotal,
        numText: numText
    };
    res.success = true;
    res.msg = "成功";
} catch (error) {
    res.success = false;
    res.msg = "失败，原因：" + error;
}
result = res;