/**
 * @definition    FormatDuration    {"category":"ext"}
 * @description   格式化时间间隔  wanghq 2025年8月21日18:37:14
 * @implementation    {Script}
 *
 * @param    {NUMBER}    milliseconds    毫秒数
 *
 * @returns    {JSON}
 */
var res = { success: false, msg: "", data: null };

try {
    // 参数验证
    if (milliseconds === null || milliseconds === undefined || milliseconds < 0) {
        throw "毫秒数必须为非负数";
    }
    
    var msec = Number(milliseconds);
    
    // 计算各个时间单位
    var days = Math.floor(msec / (1000 * 60 * 60 * 24));
    var hours = Math.floor((msec % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    var minutes = Math.floor((msec % (1000 * 60 * 60)) / (1000 * 60));
    var seconds = Math.floor((msec % (1000 * 60)) / 1000);
    var remainingMs = msec % 1000;
    
    var formattedTime = '';
    
    // 根据时间长度选择合适的显示格式
    if (days > 0) {
        formattedTime = days + '天' + hours + '时' + minutes + '分' + seconds + '秒';
    } else if (hours > 0) {
        formattedTime = hours + '时' + minutes + '分' + seconds + '秒';
    } else if (minutes > 0) {
        formattedTime = minutes + '分' + seconds + '秒';
    } else if (seconds > 0) {
        formattedTime = seconds + '秒';
    } else {
        formattedTime = msec + '毫秒';
    }
    
    res.success = true;
    res.data = {
        originalMilliseconds: msec,
        formattedTime: formattedTime,
        components: {
            days: days,
            hours: hours,
            minutes: minutes,
            seconds: seconds,
            milliseconds: remainingMs
        }
    };
    res.msg = "时间格式化成功";
    
} catch (error) {
    res.success = false;
    var msg = "FormatDuration-时间格式化失败，原因：" + error;
    logger.error(msg);
    res.msg = msg;
}

result = res;
