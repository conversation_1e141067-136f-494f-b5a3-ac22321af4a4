/**
 * @definition    UpdateModelSubmitCount
 * @description   更新型号的产品交接单数量 wanghq 2023年4月13日15:48:56
 * @implementation    {Script}
 *
 * @param    {NUMBER}    treeId        {"aspect.defaultValue":"-1.0"}
 *
 * @returns    {JSON}
 */
var res = {};
try {
    var groupTypes = ["ISCERTIFICATE", "ISLUOHAN", "ISSUBMIT"];

    var nullText = "未知";
    var fileType = "产品交接单";

    //获取所有的型号
    var modelSql = "select * from phase_model";
    if (treeId != -1) {
        modelSql += " where treeid=" + treeId;
    }
    var modelRs = Things['Thing.DB.Oracle'].RunQuery({ sql: modelSql });
    for (var j = 0; j < groupTypes.length; j++) {
        var groupType = groupTypes[j];
        var statParentType = "产品交接单_" + groupType + "_";

        var groupNames = [];
        var groupRs = Things['Thing.DB.Oracle'].RunQuery({ sql: "select distinct nvl(" + groupType + ",'" + nullText + "') as GROUP_NAME from V_LATEST_PRODUCT_SUBMIT" });
        for (var i = 0; i < groupRs.rows.length; i++) {
            var groupName = groupRs.rows[i]['GROUP_NAME'];
            groupNames.push(groupName);
        }

        for (var i = 0; i < modelRs.rows.length; i++) {
            var model = modelRs.rows[i];
            var modelId = model.TREEID;
            var submitListSql = me.GetSubmitListSql({
                treeId: modelId
            });
            var totalGroupCountSql = "select s." + groupType + " as GROUP_NAME, count(*) as COUNT from (" + submitListSql + ") s group by s." + groupType;
            var totalGroupCountRs = Things["Thing.DB.Oracle"].RunQuery({ sql: totalGroupCountSql });

            for (var x = 0; x < groupNames.length; x++) {
                var allGroupName = groupNames[x];
                var allGroupCount = 0;
                for (var m = 0; m < totalGroupCountRs.rows.length; m++) {
                    var group = totalGroupCountRs.rows[m];
                    var groupName = group["GROUP_NAME"];
                    if (allGroupName == groupName) {
                        allGroupCount = group["COUNT"];
                        break;
                    }
                }
                var statType = statParentType + allGroupName;
                me.UpdateModelStatistics({
                    modelId: modelId,
                    statType: statType,
                    statCount: allGroupCount
                });
            }

        }
    }

    res.success = true;
    res.msg = "更新型号的清单数量成功";
} catch (error) {
    res.success = false;
    res.msg = "更新型号的清单数量失败，原因：" + error;
}
result = res;