/**
 * @definition    RecordProcessService    {"category":"ext"}
 * @description   记录处理服务，负责处理单个数据记录  wanghq 2025年8月21日19:39:00
 * @implementation    {Script}
 *
 * @param    {JSON}    params    处理参数，包含行数据、记录、请求URL等
 *
 * @returns    {JSON}
 */
var result = {
    success: false,
    msg: "",
    data: {
        recordsCount: 0
    }
};

try {
    var params = params || {};
    var row = params.row;
    var reqUrl = params.reqUrl;
    var treeid = params.treeid;

    if (!row || !reqUrl) {
        throw "参数不完整";
    }

    logger.info("RecordProcessService-处理记录，NODECODE：" + row.NODECODE);

    // 1. 获取文件类型编码
    var entype = Things["Thing.Fn.SystemDic"].getFileTypeByName({
        name: row.FILE_TYPE,
        type: '自动采集清单'
    });

    if (entype === '') {
        result.msg = "未找到对应的文件类型编码：" + row.FILE_TYPE;
        result = result;
        return;
    }

    // 2. 发送SOAP请求
    var soapResult = me.SoapRequestService({
        reqUrl: reqUrl,
        processCode: row.REFTREEID,
        category: row.TABLETYPE,
        entype: entype
    });

    if (!soapResult.success) {
        result.msg = "SOAP请求失败：" + soapResult.msg;
        result = result;
        return;
    }

    var xmlData = soapResult.data;
    var records = xmlData.Record;
    result.data.recordsCount = records.length();

    // 3. 处理每条记录
    for (var j = 0; j < records.length(); j++) {
        var record = records[j];

        try {
            var recordProcessResult = processSingleRecordData(record, row, entype, treeid);
            if (!recordProcessResult.success) {
                logger.warn("RecordProcessService-记录处理失败：" + recordProcessResult.msg);
            }
        } catch (recordError) {
            logger.error("RecordProcessService-记录处理异常：" + recordError);
        }
    }

    result.success = true;
    result.msg = "记录处理完成";

    logger.info("RecordProcessService-记录处理完成，处理了 " + result.data.recordsCount + " 条记录");

} catch (error) {
    result.success = false;
    var msg = "RecordProcessService-记录处理失败，原因：" + error;
    logger.error(msg);
    result.msg = msg;
}

result = result;

// 处理单条记录数据
function processSingleRecordData(record, row, entype, treeid) {
    var recordResult = {
        success: false,
        msg: ""
    };

    try {
        var source_id = record.Id;
        if (source_id === '') {
            recordResult.msg = "记录ID为空，跳过处理";
            return recordResult;
        }

        var productId = record.ProductId || "";

        // 1. 获取文件名列
        var fileNameClo = me.getTagFileName({
            type: entype
        });

        if (!fileNameClo) {
            recordResult.msg = "无法获取文件名列名";
            return recordResult;
        }

        // 2. 处理文件
        var fileResult = me.FileProcessService({
            entype: entype,
            record: record
        });

        if (!fileResult.success) {
            recordResult.msg = "文件处理失败：" + fileResult.msg;
            return recordResult;
        }

        // 3. 检查数据是否存在
        var existResult = checkDataExists(entype, source_id + '_' + getTableType(row.TABLETYPE));
        var isExists = existResult.exists;
        var existingData = existResult.data;

        // 4. 保存数据到结果表
        var saveResult = me.DataSaveService({
            row: row,
            record: record,
            fileData: fileResult.data,
            entype: entype,
            isExists: isExists,
            existingData: existingData
        });

        if (!saveResult.success) {
            recordResult.msg = "保存数据失败：" + saveResult.msg;
            return recordResult;
        }

        // 5. 处理结构装配文件（如果需要）
        if (entype === "CheckCard" && fileResult.data.excelFilePath) {
            me.StructuralAssemblyService({
                treeid: treeid,
                fileName: record[fileNameClo],
                filePath: fileResult.data.excelFilePath,
                resultId: saveResult.data.excelResultId || saveResult.data.resultId,
                dataId: source_id + '_' + getTableType(row.TABLETYPE),
                productId: productId
            });
        }

        // 6. 插入XML数据
        me.insertXmlData({
            type: entype,
            tag: record,
            source_id: source_id + '_' + getTableType(row.TABLETYPE),
            resultId: saveResult.data.resultId,
            excelFileName: fileResult.data.excelFileName || "",
            excelFilePath: fileResult.data.excelFilePath || "",
            pdfFileName: fileResult.data.pdfFileName || "",
            pdfFilePath: fileResult.data.pdfFilePath || "",
            fileName: fileResult.data.fileName || "",
            filePath: fileResult.data.filePath || "",
            pdf_resultId: saveResult.data.pdfResultId || 0,
            excel_resultId: saveResult.data.excelResultId || 0,
            productId: productId
        });

        recordResult.success = true;
        recordResult.msg = "记录处理成功";

    } catch (error) {
        recordResult.msg = "记录处理异常：" + error;
        logger.error("RecordProcessService-处理记录数据异常：" + error);
    }

    return recordResult;
}

// 检查数据是否存在
function checkDataExists(entype, dataId) {
    try {
        var existSql = "SELECT * FROM XMLDATA_" + entype + " WHERE source_id = '" + dataId + "'";
        var rs = Things['Thing.DB.Oracle'].RunQuery({
            sql: existSql
        });
        return {
            exists: rs.rows.length > 0,
            data: rs.rows.length > 0 ? rs.rows[0] : null
        };
    } catch (error) {
        logger.error("RecordProcessService-检查数据存在性失败：" + error);
        return {
            exists: false,
            data: null
        };
    }
}

// 获取表类型
function getTableType(category) {
    switch (category) {
        case "设计":
            return "DESIGN_DATA_RESULT";
        case "工艺":
            return "CRAFT_DATA_RESULT";
        case "质量综合":
            return "QUALITY_CONTROL_RESULT";
        case "过程控制":
            return "PROCESS_CONTROL_RESULT";
        default:
            return "";
    }
}